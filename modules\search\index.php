<?php
/**
 * Global Search Results Page
 *
 * This page displays comprehensive search results from across the system.
 */

// Include necessary files
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/auth.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/functions.php');

// Ensure user is logged in
if (!isLoggedIn()) {
    header('Location: /choims/modules/auth/login.php');
    exit;
}

// Get search term
$search = isset($_GET['q']) ? trim($_GET['q']) : '';

// Validate search term
if (empty($search) || strlen($search) < 2) {
    $error = 'Please enter a search term of at least 2 characters.';
}

// Get user role and location
$userRole = $_SESSION['role'];
$userLocationId = isset($_SESSION['location_id']) ? $_SESSION['location_id'] : null;

// Initialize results arrays
$assets = [];
$consumables = [];
$locations = [];
$batch_transfers = [];

// Prepare search parameter
$searchParam = "%$search%";

// If search term is valid, perform search
if (!empty($search) && strlen($search) >= 2) {
    // Search for batch transfers
    if (hasRole('GodMode') || hasRole('Superadmin') || hasRole('Logistics') || hasRole('HIMU') || hasRole('Department') || hasRole('HealthCenter')) {
        $batchQuery = "
            SELECT
                b.batch_id,
                b.transaction_code,
                b.status,
                b.transfer_date,
                sl.location_name as source_location,
                dl.location_name as destination_location,
                u.full_name as initiated_by,
                COUNT(DISTINCT ba.asset_id) as asset_count,
                COUNT(DISTINCT bi.inventory_id) as inventory_count
            FROM
                batch_transfers b
            LEFT JOIN
                locations sl ON b.source_location_id = sl.location_id
            LEFT JOIN
                locations dl ON b.destination_location_id = dl.location_id
            LEFT JOIN
                users u ON b.initiated_by = u.user_id
            LEFT JOIN
                batch_transfer_assets ba ON b.batch_id = ba.batch_id
            LEFT JOIN
                batch_transfer_inventory bi ON b.batch_id = bi.batch_id
            WHERE
                b.transaction_code LIKE ?
        ";

        // Apply role-based restrictions
        if (!hasRole('GodMode') && !hasRole('Superadmin') && !hasRole('Logistics')) {
            if ($userLocationId) {
                // Users can only see batch transfers related to their location
                $batchQuery .= " AND (b.source_location_id = $userLocationId OR b.destination_location_id = $userLocationId)";
            }
        }

        $batchQuery .= " GROUP BY b.batch_id ORDER BY b.transfer_date DESC LIMIT 50";

        $batchStmt = mysqli_prepare($conn, $batchQuery);
        mysqli_stmt_bind_param($batchStmt, 's', $searchParam);
        mysqli_stmt_execute($batchStmt);
        $batchResult = mysqli_stmt_get_result($batchStmt);

        while ($batch = mysqli_fetch_assoc($batchResult)) {
            $batch_transfers[] = [
                'id' => $batch['batch_id'],
                'code' => $batch['transaction_code'],
                'status' => $batch['status'],
                'date' => $batch['transfer_date'],
                'source' => $batch['source_location'],
                'destination' => $batch['destination_location'],
                'initiated_by' => $batch['initiated_by'],
                'asset_count' => $batch['asset_count'],
                'inventory_count' => $batch['inventory_count'],
                'url' => "/choims/modules/transfers/batch/view.php?id=" . $batch['batch_id']
            ];
        }
    }
    // Search fixed assets
    $assetQuery = "
        SELECT
            fa.asset_id,
            fa.asset_name,
            fa.serial_number,
            s.sku_code,
            s.sku_name,
            c.category_name,
            l.location_name,
            fa.status,
            fa.purchase_date,
            fa.unit_cost
        FROM
            fixed_assets fa
        JOIN
            sku_master s ON fa.sku_id = s.sku_id
        JOIN
            categories c ON s.category_id = c.category_id
        JOIN
            locations l ON fa.current_location_id = l.location_id
        WHERE
            (fa.is_deleted = 0 OR fa.is_deleted IS NULL)
            AND (
                fa.asset_name LIKE ?
                OR fa.serial_number LIKE ?
                OR s.sku_name LIKE ?
                OR s.sku_code LIKE ?
                OR fa.remarks LIKE ?
                OR fa.assigned_to LIKE ?
                OR fa.local_mr LIKE ?
            )
    ";

    // Apply role-based restrictions
    if (!hasRole('GodMode') && !hasRole('Superadmin') && !hasRole('Logistics')) {
        if (hasRole('HIMU')) {
            // Get HIMU location ID from the user's session
            $himuLocationId = isset($_SESSION['location_id']) ? $_SESSION['location_id'] : null;

            // If we don't have a location ID from the session, try to find it by name
            if (!$himuLocationId) {
                $himuLocationQuery = "SELECT location_id FROM locations WHERE location_name LIKE '%HIMU%' OR location_name LIKE '%IT%' LIMIT 1";
                $himuLocationResult = mysqli_query($conn, $himuLocationQuery);

                if ($himuLocationResult && mysqli_num_rows($himuLocationResult) > 0) {
                    $himuLocationRow = mysqli_fetch_assoc($himuLocationResult);
                    $himuLocationId = $himuLocationRow['location_id'];
                }
            }

            // For HIMU users:
            // 1. Show all IT Equipment (category_id = 1) regardless of location
            // 2. Only show Office Equipment and Medical Equipment in HIMU's location
            if ($himuLocationId) {
                $assetQuery .= " AND (c.category_id = 1 OR (c.category_id IN (2, 3) AND fa.current_location_id = $himuLocationId))";
            } else {
                // If we can't find HIMU's location, just show IT Equipment
                $assetQuery .= " AND c.category_id = 1";
            }
        } elseif ($userLocationId) {
            // Department and Health Center users can only see their location's assets
            $assetQuery .= " AND fa.current_location_id = $userLocationId";
        }
    }

    $assetQuery .= " ORDER BY fa.asset_name LIMIT 50"; // Limit to 50 results

    $assetStmt = mysqli_prepare($conn, $assetQuery);
    mysqli_stmt_bind_param($assetStmt, 'sssssss', $searchParam, $searchParam, $searchParam, $searchParam, $searchParam, $searchParam, $searchParam);
    mysqli_stmt_execute($assetStmt);
    $assetResult = mysqli_stmt_get_result($assetStmt);

    while ($asset = mysqli_fetch_assoc($assetResult)) {
        $assets[] = [
            'id' => $asset['asset_id'],
            'name' => $asset['asset_name'],
            'sku' => $asset['sku_code'],
            'sku_name' => $asset['sku_name'],
            'serial' => $asset['serial_number'],
            'category' => $asset['category_name'],
            'location' => $asset['location_name'],
            'status' => $asset['status'],
            'purchase_date' => $asset['purchase_date'],
            'unit_cost' => $asset['unit_cost'],
            'url' => "/choims/modules/assets/view.php?id=" . $asset['asset_id']
        ];
    }

    // Search consumables/inventory
    $inventoryQuery = "
        SELECT
            i.inventory_id,
            s.sku_code,
            s.sku_name,
            c.category_name,
            l.location_name,
            i.quantity,
            i.minimum_stock,
            i.maximum_stock
        FROM
            inventory i
        JOIN
            sku_master s ON i.sku_id = s.sku_id
        JOIN
            categories c ON s.category_id = c.category_id
        JOIN
            locations l ON i.location_id = l.location_id
        WHERE
            (i.is_deleted = 0 OR i.is_deleted IS NULL)
            AND (
                s.sku_name LIKE ?
                OR s.sku_code LIKE ?
            )
    ";

    // Apply role-based restrictions
    if (!hasRole('GodMode') && !hasRole('Superadmin') && !hasRole('Logistics')) {
        if (hasRole('HIMU')) {
            // HIMU can see all IT equipment
            $inventoryQuery .= " AND c.category_id = 1"; // Assuming category_id 1 is IT Equipment
        } elseif ($userLocationId) {
            // Department and Health Center users can only see their location's inventory
            $inventoryQuery .= " AND i.location_id = $userLocationId";
        }
    }

    $inventoryQuery .= " ORDER BY s.sku_name LIMIT 50"; // Limit to 50 results

    $inventoryStmt = mysqli_prepare($conn, $inventoryQuery);
    mysqli_stmt_bind_param($inventoryStmt, 'ss', $searchParam, $searchParam);
    mysqli_stmt_execute($inventoryStmt);
    $inventoryResult = mysqli_stmt_get_result($inventoryStmt);

    while ($item = mysqli_fetch_assoc($inventoryResult)) {
        $consumables[] = [
            'id' => $item['inventory_id'],
            'sku' => $item['sku_code'],
            'name' => $item['sku_name'],
            'category' => $item['category_name'],
            'location' => $item['location_name'],
            'quantity' => $item['quantity'],
            'min_stock' => $item['minimum_stock'],
            'max_stock' => $item['maximum_stock'],
            'url' => "/choims/modules/inventory/view.php?id=" . $item['inventory_id']
        ];
    }

    // Search locations (if user has appropriate permissions)
    if (hasRole('GodMode') || hasRole('Superadmin') || hasRole('Logistics') || hasRole('HIMU')) {
        $locationQuery = "
            SELECT
                location_id,
                location_name,
                location_type,
                address,
                contact_person,
                contact_number
            FROM
                locations
            WHERE
                location_name LIKE ?
            ORDER BY location_name
            LIMIT 50
        ";

        $locationStmt = mysqli_prepare($conn, $locationQuery);
        mysqli_stmt_bind_param($locationStmt, 's', $searchParam);
        mysqli_stmt_execute($locationStmt);
        $locationResult = mysqli_stmt_get_result($locationStmt);

        while ($location = mysqli_fetch_assoc($locationResult)) {
            $locations[] = [
                'id' => $location['location_id'],
                'name' => $location['location_name'],
                'type' => $location['location_type'],
                'address' => $location['address'],
                'contact_person' => $location['contact_person'],
                'contact_number' => $location['contact_number'],
                'url' => "/choims/modules/locations/view.php?id=" . $location['location_id']
            ];
        }
    }
}

// Calculate total results
$totalResults = count($assets) + count($consumables) + count($locations) + count($batch_transfers);
?>

<div class="container-fluid animate__animated animate__fadeIn">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="page-title">
                <i class="fas fa-search"></i>
                Search Results
            </h1>

            <!-- Search Form -->
            <div class="card mb-4">
                <div class="card-body">
                    <form action="/choims/modules/search/index.php" method="GET" class="row g-3 align-items-center">
                        <div class="col-md-8">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control form-control-lg" name="q" value="<?php echo htmlspecialchars($search); ?>" placeholder="Search for assets, consumables, locations..." required>
                                <button type="submit" class="btn btn-primary btn-lg">Search</button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-text">
                                <?php if (!empty($search)): ?>
                                    Found <?php echo $totalResults; ?> results for "<?php echo htmlspecialchars($search); ?>"
                                <?php else: ?>
                                    Enter a search term to find assets, consumables, and locations
                                <?php endif; ?>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <?php if (!empty($error)): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($search) && $totalResults === 0): ?>
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h3>No results found</h3>
                        <p class="text-muted">We couldn't find any matches for "<?php echo htmlspecialchars($search); ?>"</p>
                        <p>Try adjusting your search term or check the spelling</p>
                    </div>
                </div>
            <?php endif; ?>

            <?php if (count($assets) > 0): ?>
                <!-- Assets Results -->
                <div class="card mb-4 animate__animated animate__fadeInUp animate__faster">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-laptop me-2"></i> Assets</h5>
                        <span class="badge bg-primary"><?php echo count($assets); ?> found</span>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>SKU</th>
                                        <th>Serial Number</th>
                                        <th>Category</th>
                                        <th>Location</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($assets as $asset): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($asset['name']); ?></td>
                                            <td><?php echo htmlspecialchars($asset['sku']); ?></td>
                                            <td><?php echo htmlspecialchars($asset['serial']); ?></td>
                                            <td><?php echo htmlspecialchars($asset['category']); ?></td>
                                            <td><?php echo htmlspecialchars($asset['location']); ?></td>
                                            <td>
                                                <?php
                                                $statusClass = 'bg-info';
                                                if ($asset['status'] === 'In use') $statusClass = 'bg-success';
                                                if ($asset['status'] === 'Under Repair') $statusClass = 'bg-warning';
                                                if ($asset['status'] === 'Defective') $statusClass = 'bg-danger';
                                                ?>
                                                <span class="badge <?php echo $statusClass; ?>"><?php echo htmlspecialchars($asset['status']); ?></span>
                                            </td>
                                            <td>
                                                <a href="<?php echo $asset['url']; ?>" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if (count($consumables) > 0): ?>
                <!-- Consumables Results -->
                <div class="card mb-4 animate__animated animate__fadeInUp animate__faster animate__delay-1">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-box me-2"></i> Consumables</h5>
                        <span class="badge bg-primary"><?php echo count($consumables); ?> found</span>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>SKU</th>
                                        <th>Category</th>
                                        <th>Location</th>
                                        <th>Quantity</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($consumables as $item): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($item['name']); ?></td>
                                            <td><?php echo htmlspecialchars($item['sku']); ?></td>
                                            <td><?php echo htmlspecialchars($item['category']); ?></td>
                                            <td><?php echo htmlspecialchars($item['location']); ?></td>
                                            <td>
                                                <?php
                                                $quantityClass = 'bg-info';
                                                if ($item['quantity'] <= 0) $quantityClass = 'bg-danger';
                                                elseif ($item['quantity'] < $item['min_stock']) $quantityClass = 'bg-warning';
                                                else $quantityClass = 'bg-success';
                                                ?>
                                                <span class="badge <?php echo $quantityClass; ?>"><?php echo htmlspecialchars($item['quantity']); ?></span>
                                            </td>
                                            <td>
                                                <a href="<?php echo $item['url']; ?>" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if (count($locations) > 0): ?>
                <!-- Locations Results -->
                <div class="card mb-4 animate__animated animate__fadeInUp animate__faster animate__delay-2">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i> Locations</h5>
                        <span class="badge bg-primary"><?php echo count($locations); ?> found</span>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Type</th>
                                        <th>Address</th>
                                        <th>Contact Person</th>
                                        <th>Contact Number</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($locations as $location): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($location['name']); ?></td>
                                            <td><?php echo htmlspecialchars($location['type']); ?></td>
                                            <td><?php echo htmlspecialchars($location['address'] ?: 'N/A'); ?></td>
                                            <td><?php echo htmlspecialchars($location['contact_person'] ?: 'N/A'); ?></td>
                                            <td><?php echo htmlspecialchars($location['contact_number'] ?: 'N/A'); ?></td>
                                            <td>
                                                <a href="<?php echo $location['url']; ?>" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if (count($batch_transfers) > 0): ?>
                <!-- Batch Transfers Results -->
                <div class="card mb-4 animate__animated animate__fadeInUp animate__faster animate__delay-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-boxes me-2"></i> Batch Transfers</h5>
                        <span class="badge bg-primary"><?php echo count($batch_transfers); ?> found</span>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Transaction Code</th>
                                        <th>Date</th>
                                        <th>Source</th>
                                        <th>Destination</th>
                                        <th>Assets</th>
                                        <th>Inventory Items</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($batch_transfers as $batch): ?>
                                        <tr>
                                            <td>
                                                <span class="badge bg-primary"><?php echo htmlspecialchars($batch['code']); ?></span>
                                            </td>
                                            <td><?php echo date('M d, Y', strtotime($batch['date'])); ?></td>
                                            <td><?php echo htmlspecialchars($batch['source']); ?></td>
                                            <td><?php echo htmlspecialchars($batch['destination']); ?></td>
                                            <td><?php echo $batch['asset_count']; ?></td>
                                            <td><?php echo $batch['inventory_count']; ?></td>
                                            <td>
                                                <?php
                                                $statusClass = 'bg-info';
                                                if ($batch['status'] === 'Completed') $statusClass = 'bg-success';
                                                if ($batch['status'] === 'Rejected') $statusClass = 'bg-danger';
                                                if ($batch['status'] === 'Pending') $statusClass = 'bg-warning';
                                                ?>
                                                <span class="badge <?php echo $statusClass; ?>"><?php echo htmlspecialchars($batch['status']); ?></span>
                                            </td>
                                            <td>
                                                <a href="<?php echo $batch['url']; ?>" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// Include footer
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>
