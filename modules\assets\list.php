<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Add the modern assets list CSS
echo '<link rel="stylesheet" href="/choims/assets/css/assets-list-modern.css">';

// Ensure user is logged in
requireLogin();

// Get filters
$category_id = isset($_GET['category']) ? sanitizeInput($_GET['category']) : '';

// For HIMU users, we'll handle their specific filtering in the WHERE clause
if (empty($category_id) && hasRole('HIMU')) {
    // Don't set a default category here
}
$location_id = isset($_GET['location']) ? sanitizeInput($_GET['location']) : '';
$status = isset($_GET['status']) ? sanitizeInput($_GET['status']) : '';
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';

// Check if is_deleted column exists
$checkColumnQuery = "SHOW COLUMNS FROM fixed_assets LIKE 'is_deleted'";
$checkColumnResult = mysqli_query($conn, $checkColumnQuery);
$isDeletedColumnExists = mysqli_num_rows($checkColumnResult) > 0;

// Build query based on filters
$query = "
    SELECT
        a.asset_id,
        a.sku_id,
        s.sku_code,
        s.sku_name,
        s.category_id,
        c.category_name,
        a.asset_name,
        a.serial_number,
        a.model,
        a.specifications,
        a.status,
        a.assigned_to,
        a.current_location_id,
        l.location_name,
        a.unit_section,
        a.local_mr,
        a.purchase_date,
        a.unit_cost,
        a.receipt_type,
        a.series_number,
        a.supplier_id,
        sup.supplier_name,
        a.warranty_expiry,
        a.source_id,
        src.source_name,
        (SELECT COUNT(*) FROM transfers t WHERE t.asset_id = a.asset_id AND t.status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU')) as has_active_transfer,
        (SELECT t.transfer_id FROM transfers t WHERE t.asset_id = a.asset_id AND t.status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU') LIMIT 1) as active_transfer_id,
        (SELECT COUNT(*) FROM batch_transfer_assets bta
         JOIN batch_transfers bt ON bta.batch_id = bt.batch_id
         WHERE bta.asset_id = a.asset_id AND bt.status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU')) as has_active_batch_transfer,
        (SELECT bt.batch_id FROM batch_transfer_assets bta
         JOIN batch_transfers bt ON bta.batch_id = bt.batch_id
         WHERE bta.asset_id = a.asset_id AND bt.status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU') LIMIT 1) as active_batch_id,
        a.remarks,
        a.created_by,
        a.created_at,
        a.updated_at
";

// Only include the is_deleted column if it exists
if ($isDeletedColumnExists) {
    $query .= ", a.is_deleted";
}

$query .= "
    FROM
        fixed_assets a
        LEFT JOIN sku_master s ON a.sku_id = s.sku_id
        LEFT JOIN categories c ON s.category_id = c.category_id
        LEFT JOIN locations l ON a.current_location_id = l.location_id
        LEFT JOIN suppliers sup ON a.supplier_id = sup.supplier_id
        LEFT JOIN sources src ON a.source_id = src.source_id
    WHERE 1=1
";

// Only filter by is_deleted if the column exists
if ($isDeletedColumnExists) {
    // Only show deleted items to superadmin when explicitly requested
    if (isset($_GET['show_deleted']) && $_GET['show_deleted'] == 1 && hasRole('Superadmin')) {
        $query .= " AND a.is_deleted = 1";
    } else {
        // For everyone else, only show non-deleted items
        $query .= " AND (a.is_deleted = 0 OR a.is_deleted IS NULL)";
    }
}

// Apply filters
$params = [];
$types = '';

if (!empty($category_id)) {
    $query .= " AND s.category_id = ?";
    $params[] = $category_id;
    $types .= 'i';
}

if (!empty($location_id)) {
    $query .= " AND a.current_location_id = ?";
    $params[] = $location_id;
    $types .= 'i';
}

if (!empty($status)) {
    $query .= " AND a.status = ?";
    $params[] = $status;
    $types .= 's';
}

if (!empty($search)) {
    $query .= " AND (a.asset_name LIKE ? OR a.serial_number LIKE ? OR s.sku_name LIKE ? OR s.sku_code LIKE ? OR a.remarks LIKE ?)";
    $searchParam = "%$search%";
    $params[] = $searchParam;
    $params[] = $searchParam;
    $params[] = $searchParam;
    $params[] = $searchParam;
    $params[] = $searchParam;
    $types .= 'sssss';
}

// Special handling for HIMU users
if (hasRole('HIMU')) {
    // Get HIMU location ID from the user's session
    $himuLocationId = isset($_SESSION['location_id']) ? $_SESSION['location_id'] : null;

    // If we don't have a location ID from the session, try to find it by name
    if (!$himuLocationId) {
        $himuLocationQuery = "SELECT location_id FROM locations WHERE location_name LIKE '%HIMU%' OR location_name LIKE '%IT%' LIMIT 1";
        $himuLocationResult = mysqli_query($conn, $himuLocationQuery);

        if ($himuLocationResult && mysqli_num_rows($himuLocationResult) > 0) {
            $himuLocationRow = mysqli_fetch_assoc($himuLocationResult);
            $himuLocationId = $himuLocationRow['location_id'];
        }
    }

    // For HIMU users:
    // 1. Show all IT Equipment (category_id = 1) regardless of location
    // 2. Only show Office Equipment (category_id = 2) and Medical Equipment (category_id = 3) if they are in HIMU's location
    if (!empty($category_id)) {
        // If a specific category is selected and it's Office Equipment or Medical Equipment,
        // still restrict to HIMU's location
        if ($category_id == 2 || $category_id == 3) {
            if ($himuLocationId) {
                $query .= " AND a.current_location_id = ?";
                $params[] = $himuLocationId;
                $types .= 'i';
            }
        }
        // For IT Equipment, no location restriction needed
    } else {
        // For no category filter, apply the special HIMU rules
        if ($himuLocationId) {
            $query .= " AND (s.category_id = 1 OR (s.category_id IN (2, 3) AND a.current_location_id = ?))";
            $params[] = $himuLocationId;
            $types .= 'i';
        } else {
            // If we can't find HIMU's location, just show IT Equipment
            $query .= " AND s.category_id = 1";
        }
    }
}
// Restrict to user's location if not admin or HIMU
else if (!hasRole('GodMode') && !hasRole('Superadmin') && !hasRole('Logistics')) {
    $userLocationId = $_SESSION['location_id'];
    if ($userLocationId) {
        $query .= " AND a.current_location_id = ?";
        $params[] = $userLocationId;
        $types .= 'i';
    }
}

$query .= " ORDER BY has_active_transfer DESC, a.created_at DESC";

// Prepare and execute query
$stmt = mysqli_prepare($conn, $query);
if (!empty($params)) {
    // Fix for PHP versions that don't support spread operator
    $ref_params = array();
    $ref_params[] = &$stmt;
    $ref_params[] = &$types;
    foreach($params as $key => $value) {
        $ref_params[] = &$params[$key];
    }
    call_user_func_array('mysqli_stmt_bind_param', $ref_params);
}
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

// Get categories for filter
// All users can see all fixed asset categories in the dropdown
$categoriesQuery = "SELECT * FROM categories WHERE category_id IN (1, 2, 3) ORDER BY category_name";
$categoriesResult = mysqli_query($conn, $categoriesQuery);

// Get locations for filter
$locationsQuery = "SELECT * FROM locations ORDER BY location_name";
$locationsResult = mysqli_query($conn, $locationsQuery);
?>

<div class="container-fluid">
    <!-- Modern dashboard header -->
    <header class="dashboard-header">
        <div class="header-row">
            <div class="title-container">
                <div class="dashboard-title-icon">
                    <i class="fas fa-laptop-house"></i>
                </div>
                <h1 class="dashboard-title">Fixed Assets</h1>
            </div>
            <div class="dashboard-actions">
                <?php if (hasRole('Logistics')): ?>
                <a href="/choims/modules/assets/add.php" class="action-btn action-primary">
                    <i class="fas fa-plus-circle"></i> Add New Asset
                </a>
                <?php endif; ?>
                <?php if (hasRole('Superadmin') && !isset($_GET['show_deleted'])): ?>
                <a href="/choims/modules/assets/list.php?show_deleted=1" class="action-btn">
                    <i class="fas fa-trash"></i> View Deleted
                </a>
                <?php endif; ?>
                <button id="printTable" class="action-btn">
                    <i class="fas fa-print"></i> Print Report
                </button>
            </div>
        </div>
        <p class="dashboard-date"><i class="far fa-calendar-alt"></i> Today is <?php echo date('F d, Y'); ?></p>
    </header>

    <?php if (isset($_GET['show_deleted']) && $_GET['show_deleted'] == 1 && hasRole('Superadmin')): ?>
        <div class="alert alert-warning d-flex align-items-center" role="alert">
            <div class="alert-icon">
                <i class="fas fa-trash-alt"></i>
            </div>
            <div class="d-flex w-100 justify-content-between align-items-center">
                <div>You are viewing deleted assets. <a href="/choims/modules/assets/list.php" class="alert-link fw-bold">View active assets</a></div>
                <button type="button" class="btn-close small" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success d-flex align-items-center" role="alert">
            <div class="alert-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="d-flex w-100 justify-content-between align-items-center">
                <div><?php echo $_SESSION['success']; unset($_SESSION['success']); ?></div>
                <button type="button" class="btn-close small" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger d-flex align-items-center" role="alert">
            <div class="alert-icon">
                <i class="fas fa-exclamation-circle"></i>
            </div>
            <div class="d-flex w-100 justify-content-between align-items-center">
                <div><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></div>
                <button type="button" class="btn-close small" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    <?php endif; ?>

    <?php if (isset($_GET['success'])): ?>
        <div class="alert alert-success d-flex align-items-center" role="alert">
            <div class="alert-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="d-flex w-100 justify-content-between align-items-center">
                <div>Operation completed successfully!</div>
                <button type="button" class="btn-close small" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    <?php endif; ?>

    <?php
    // Calculate summary statistics
    mysqli_data_seek($result, 0);
    $total_assets = 0;
    $in_use_assets = 0;
    $available_assets = 0;
    $under_repair_assets = 0;
    $defective_assets = 0;
    $categories_count = [];

    while ($asset = mysqli_fetch_assoc($result)) {
        $total_assets++;

        switch ($asset['status']) {
            case 'In use':
                $in_use_assets++;
                break;
            case 'Available':
                $available_assets++;
                break;
            case 'Under Repair':
                $under_repair_assets++;
                break;
            case 'Defective':
                $defective_assets++;
                break;
        }

        // Count assets by category
        if (!isset($categories_count[$asset['category_name']])) {
            $categories_count[$asset['category_name']] = 0;
        }
        $categories_count[$asset['category_name']]++;
    }

    // Reset the result pointer for later use
    mysqli_data_seek($result, 0);
    ?>

    <!-- Statistics Cards -->
    <div class="stats-container">
        <div class="stat-card stat-primary">
            <div class="stat-icon">
                <i class="fas fa-laptop-house"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value"><?php echo $total_assets; ?></div>
                <div class="stat-label">Total Assets</div>
            </div>
        </div>

        <div class="stat-card stat-info">
            <div class="stat-icon">
                <i class="fas fa-user"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value"><?php echo $in_use_assets; ?></div>
                <div class="stat-label">In Use</div>
            </div>
        </div>

        <div class="stat-card stat-success">
            <div class="stat-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value"><?php echo $available_assets; ?></div>
                <div class="stat-label">Available</div>
            </div>
        </div>

        <div class="stat-card stat-warning">
            <div class="stat-icon">
                <i class="fas fa-tools"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value"><?php echo $under_repair_assets + $defective_assets; ?></div>
                <div class="stat-label">Under Repair/Defective</div>
            </div>
        </div>
    </div>

    <!-- Category Breakdown Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm rounded-4">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center justify-content-between mb-4">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon bg-info-bg rounded-3 p-3 me-3">
                                <i class="fas fa-chart-pie text-info"></i>
                            </div>
                            <h5 class="card-title mb-0">Category Breakdown</h5>
                        </div>
                    </div>

                    <div class="row">
                        <?php foreach ($categories_count as $category => $count): ?>
                            <div class="col-md-4 col-sm-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <?php
                                    $iconClass = 'fa-laptop';
                                    if (stripos($category, 'medical') !== false) {
                                        $iconClass = 'fa-heartbeat';
                                    } elseif (stripos($category, 'office') !== false) {
                                        $iconClass = 'fa-building';
                                    }
                                    ?>
                                    <div class="category-icon rounded-circle p-2 me-3" style="background-color: rgba(<?php echo rand(0, 200); ?>, <?php echo rand(0, 200); ?>, <?php echo rand(0, 200); ?>, 0.1);">
                                        <i class="fas <?php echo $iconClass; ?> text-primary"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0"><?php echo $category; ?></h6>
                                        <div class="d-flex align-items-center">
                                            <span class="text-muted small"><?php echo $count; ?> assets</span>
                                            <div class="ms-2 badge bg-light text-dark"><?php echo round(($count / $total_assets) * 100); ?>%</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <div class="card">
        <div class="card-header">
            <div class="card-header-title">
                <i class="fas fa-table"></i>
                Asset List
            </div>
            <div class="d-flex align-items-center">
                <div class="view-toggle-container me-3">
                    <div class="btn-group view-toggle-group">
                        <button type="button" class="btn btn-sm btn-light active" id="itemsViewBtn">
                            <i class="fas fa-laptop-house me-1"></i> Assets
                        </button>
                        <button type="button" class="btn btn-sm btn-light" id="locationsViewBtn">
                            <i class="fas fa-map-marker-alt me-1"></i> Locations
                        </button>
                    </div>
                </div>

            </div>
        </div>
        <div class="card-body">
            <!-- Filter Panel (Always Visible) -->
            <div class="filter-section" id="filterSection">
                <div class="filter-header">
                    <div class="filter-title">
                        <i class="fas fa-filter"></i>
                        Filter Assets
                        <?php if(!empty($category_id) || !empty($location_id) || !empty($status) || !empty($search)): ?>
                            <span class="filter-badge">Active</span>
                        <?php endif; ?>
                    </div>

                    <?php if(!empty($category_id) || !empty($location_id) || !empty($status) || !empty($search)): ?>
                        <a href="/choims/modules/assets/list.php" class="btn btn-sm btn-outline-secondary btn-rounded">
                            <i class="fas fa-times me-1"></i> Clear Filters
                        </a>
                    <?php endif; ?>
                </div>

                <div class="filter-card">
                    <form method="get" action="" class="">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="category" class="form-label small fw-medium">Category</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-tags text-success"></i></span>
                                    <select class="form-select rounded-end-3 border-start-0" id="category" name="category">
                                        <option value="">All Categories</option>
                                        <?php mysqli_data_seek($categoriesResult, 0); ?>
                                        <?php while ($category = mysqli_fetch_assoc($categoriesResult)): ?>
                                            <option value="<?php echo $category['category_id']; ?>" <?php echo ($category_id == $category['category_id']) ? 'selected' : ''; ?>>
                                                <?php echo $category['category_name']; ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label for="location" class="form-label small fw-medium">Location</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-map-marker-alt text-success"></i></span>
                                    <select class="form-select rounded-end-3 border-start-0" id="location" name="location">
                                        <option value="">All Locations</option>
                                        <?php mysqli_data_seek($locationsResult, 0); ?>
                                        <?php while ($location = mysqli_fetch_assoc($locationsResult)): ?>
                                            <option value="<?php echo $location['location_id']; ?>" <?php echo ($location_id == $location['location_id']) ? 'selected' : ''; ?>>
                                                <?php echo $location['location_name']; ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label for="status" class="form-label small fw-medium">Status</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-info-circle text-success"></i></span>
                                    <select class="form-select rounded-end-3 border-start-0" id="status" name="status">
                                        <option value="">All Status</option>
                                        <option value="In use" <?php echo ($status == 'In use') ? 'selected' : ''; ?>>In use</option>
                                        <option value="Available" <?php echo ($status == 'Available') ? 'selected' : ''; ?>>Available</option>
                                        <option value="Under Repair" <?php echo ($status == 'Under Repair') ? 'selected' : ''; ?>>Under Repair</option>
                                        <option value="Defective" <?php echo ($status == 'Defective') ? 'selected' : ''; ?>>Defective</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label for="search" class="form-label small fw-medium">Search</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-search text-success"></i></span>
                                    <input type="text" class="form-control border-start-0 border-end-0" id="search" name="search" placeholder="Asset name, serial #, SKU, remarks" value="<?php echo $search; ?>">
                                    <button type="submit" class="btn btn-success rounded-end-3">
                                        <i class="fas fa-filter me-1"></i> Apply Filter
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Items View -->
            <div id="itemsView">
                <!-- Batch Transfer Controls -->
                <div id="batchTransferControls" class="mb-3" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center p-3 bg-light rounded-3">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-primary me-2" id="selectedCount">0</span>
                            <span class="text-muted">assets selected</span>
                        </div>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="clearSelection">
                                <i class="fas fa-times me-1"></i> Clear Selection
                            </button>
                            <form method="post" action="/choims/modules/transfers/batch/process.php" style="display: inline;" id="batchTransferForm">
                                <input type="hidden" name="item_type" value="assets">
                                <button type="submit" class="btn btn-sm btn-success" id="batchTransferBtn">
                                    <i class="fas fa-exchange-alt me-1"></i> Batch Transfer
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <div class="table-loading-state" id="tableLoadingState">
                        <div class="spinner-border text-success" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p>Loading assets...</p>
                    </div>
                    <table class="table table-hover align-middle" id="assetsTable" style="display: none;">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center" style="width: 50px;">
                                <input type="checkbox" class="form-check-input" id="selectAll" title="Select All">
                            </th>
                            <th class="text-center" style="width: 100px;">SKU</th>
                            <th>Asset Name</th>
                            <th>Category</th>
                            <th>Serial Number</th>
                            <th>Location</th>
                            <th class="text-center">Status</th>
                            <th class="text-center">Added Date</th>
                            <th class="text-center" style="width: 140px;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (mysqli_num_rows($result) > 0): ?>
                            <?php while ($asset = mysqli_fetch_assoc($result)): ?>
                                <tr>
                                    <td class="text-center">
                                        <?php
                                        // Only show checkbox if asset can be transferred (no active transfer and user has permission)
                                        $canTransfer = !$asset['has_active_transfer'];
                                        if (hasRole('Logistics')) {
                                            $userLocationId = isset($_SESSION['location_id']) ? $_SESSION['location_id'] : null;
                                            $canTransfer = $canTransfer && ($asset['current_location_id'] == $userLocationId);
                                        }
                                        ?>
                                        <?php if ($canTransfer): ?>
                                            <input type="checkbox" class="form-check-input asset-checkbox"
                                                   value="<?php echo $asset['asset_id']; ?>"
                                                   data-asset-name="<?php echo htmlspecialchars($asset['asset_name']); ?>">
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <code class="bg-light px-2 py-1 rounded-3 small"><?php echo $asset['sku_code']; ?></code>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php
                                            $iconClass = 'fa-laptop';
                                            if (stripos($asset['asset_name'], 'server') !== false) {
                                                $iconClass = 'fa-server';
                                            } elseif (stripos($asset['asset_name'], 'printer') !== false) {
                                                $iconClass = 'fa-print';
                                            } elseif (stripos($asset['asset_name'], 'phone') !== false || stripos($asset['asset_name'], 'mobile') !== false) {
                                                $iconClass = 'fa-mobile-alt';
                                            } elseif (stripos($asset['asset_name'], 'router') !== false || stripos($asset['asset_name'], 'switch') !== false || stripos($asset['asset_name'], 'network') !== false) {
                                                $iconClass = 'fa-network-wired';
                                            } elseif (stripos($asset['asset_name'], 'monitor') !== false || stripos($asset['asset_name'], 'display') !== false || stripos($asset['asset_name'], 'screen') !== false) {
                                                $iconClass = 'fa-desktop';
                                            }
                                            ?>
                                            <div class="asset-icon">
                                                <i class="fas <?php echo $iconClass; ?>"></i>
                                            </div>
                                            <div>
                                                <div class="fw-medium"><?php echo $asset['asset_name']; ?></div>
                                                <?php if (isset($asset['model']) && !empty($asset['model'])): ?>
                                                <div class="text-muted small"><?php echo $asset['model']; ?></div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-tag text-success me-2"></i>
                                            <div>
                                                <div><?php echo $asset['category_name']; ?></div>
                                                <small class="text-muted"><?php echo $asset['sku_name']; ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if (isset($asset['serial_number']) && !empty($asset['serial_number'])): ?>
                                            <code class="bg-light px-2 py-1 rounded-3 small"><?php echo $asset['serial_number']; ?></code>
                                        <?php else: ?>
                                            <span class="text-muted">N/A</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-map-marker-alt text-success me-2"></i>
                                            <?php echo $asset['location_name']; ?>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <?php
                                        $statusClass = '';
                                        $statusIcon = '';

                                        switch ($asset['status']) {
                                            case 'In use':
                                                $statusClass = 'bg-primary bg-opacity-10 text-primary';
                                                $statusIcon = 'fa-user';
                                                break;
                                            case 'Available':
                                                $statusClass = 'bg-success bg-opacity-10 text-success';
                                                $statusIcon = 'fa-check-circle';
                                                break;
                                            case 'Under Repair':
                                                $statusClass = 'bg-warning bg-opacity-10 text-warning';
                                                $statusIcon = 'fa-tools';
                                                break;
                                            case 'Defective':
                                                $statusClass = 'bg-danger bg-opacity-10 text-danger';
                                                $statusIcon = 'fa-exclamation-triangle';
                                                break;
                                            default:
                                                $statusClass = 'bg-secondary bg-opacity-10 text-secondary';
                                                $statusIcon = 'fa-question-circle';
                                        }
                                        ?>
                                        <span class="badge rounded-pill <?php echo $statusClass; ?> px-3 py-2">
                                            <i class="fas <?php echo $statusIcon; ?> me-1"></i>
                                            <?php echo $asset['status']; ?>
                                        </span>
                                        <?php if ($asset['has_active_transfer'] > 0 || $asset['has_active_batch_transfer'] > 0): ?>
                                        <div class="mt-1">
                                            <?php if ($asset['has_active_transfer'] > 0): ?>
                                                <a href="/choims/modules/transfers/view.php?id=<?php echo $asset['active_transfer_id']; ?>" class="text-decoration-none">
                                                    <span class="badge rounded-pill bg-info px-3 py-2" data-bs-toggle="tooltip" title="Click to view transfer details">
                                                        <i class="fas fa-exchange-alt me-1"></i>
                                                        Transfer Ongoing
                                                    </span>
                                                </a>
                                            <?php elseif ($asset['has_active_batch_transfer'] > 0): ?>
                                                <a href="/choims/modules/transfers/batch/view.php?id=<?php echo $asset['active_batch_id']; ?>" class="text-decoration-none">
                                                    <span class="badge rounded-pill bg-info px-3 py-2" data-bs-toggle="tooltip" title="Click to view batch transfer details">
                                                        <i class="fas fa-exchange-alt me-1"></i>
                                                        Batch Transfer Ongoing
                                                    </span>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center" data-sort="<?php echo isset($asset['created_at']) ? strtotime($asset['created_at']) : 0; ?>">
                                        <?php if (isset($asset['created_at']) && !empty($asset['created_at'])): ?>
                                            <div class="d-flex align-items-center justify-content-center">
                                                <i class="fas fa-calendar-alt text-success me-2"></i>
                                                <div>
                                                    <div><?php echo date('M d, Y', strtotime($asset['created_at'])); ?></div>
                                                    <small class="text-muted"><?php echo date('h:i A', strtotime($asset['created_at'])); ?></small>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">N/A</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="d-flex justify-content-center gap-1">
                                            <!-- View button (always visible) -->
                                            <?php
                                            // Build return URL with current filters
                                            $returnParams = [];
                                            if (!empty($category_id)) $returnParams['category'] = $category_id;
                                            if (!empty($location_id)) $returnParams['location'] = $location_id;
                                            if (!empty($status)) $returnParams['status'] = $status;
                                            if (!empty($search)) $returnParams['search'] = $search;
                                            $returnUrl = '/choims/modules/assets/list.php' . (!empty($returnParams) ? '?' . http_build_query($returnParams) : '');
                                            ?>
                                            <a href="/choims/modules/assets/view.php?id=<?php echo $asset['asset_id']; ?>&return_url=<?php echo urlencode($returnUrl); ?>" class="btn btn-sm btn-light" data-bs-toggle="tooltip" title="View Details">
                                                <i class="fas fa-eye text-success"></i>
                                            </a>

                                            <?php
                                                // Get the current user's location ID
                                                $userLocationId = isset($_SESSION['location_id']) ? $_SESSION['location_id'] : null;

                                                // For Logistics users, check if asset is at their location
                                                $canModifyAsset = true;
                                                if (hasRole('Logistics') && $userLocationId) {
                                                    $canModifyAsset = ($asset['current_location_id'] == $userLocationId) && !$asset['has_active_transfer'];
                                                } else {
                                                    $canModifyAsset = !$asset['has_active_transfer'];
                                                }

                                                // Set appropriate tooltips
                                                $editTooltip = '';
                                                $transferTooltip = '';

                                                if ($asset['has_active_transfer']) {
                                                    $editTooltip = 'Cannot edit - Transfer in progress';
                                                    $transferTooltip = 'Cannot transfer - Transfer already in progress';
                                                } elseif (hasRole('Logistics') && $userLocationId && $asset['current_location_id'] != $userLocationId) {
                                                    $editTooltip = 'Cannot edit - Asset is at a different location';
                                                    $transferTooltip = 'Cannot transfer - Asset is at a different location';
                                                } else {
                                                    $editTooltip = 'Edit Asset';
                                                    $transferTooltip = 'Transfer Asset';
                                                }

                                                // Get current user role
                                                $userRole = strtolower($_SESSION['role']);
                                            ?>

                                            <?php if (isset($_GET['show_deleted']) && $_GET['show_deleted'] == 1 && $userRole === 'superadmin'): ?>
                                                <!-- Restore button (only for Superadmin when viewing deleted assets) -->
                                                <button class="btn btn-sm btn-light restore-asset" data-asset-id="<?php echo $asset['asset_id']; ?>" data-bs-toggle="modal" data-bs-target="#restoreAssetModal" title="Restore Asset">
                                                    <i class="fas fa-trash-restore text-success"></i>
                                                </button>
                                            <?php else: ?>
                                                <?php
                                                // Show buttons based on user role
                                                switch ($userRole) {
                                                    case 'superadmin':
                                                        // Superadmin only gets transfer button
                                                        ?>
                                                        <a href="<?php echo !$canModifyAsset ? 'javascript:void(0);' : '/choims/modules/transfers/create.php?type=asset&asset_id=' . $asset['asset_id']; ?>" class="btn btn-sm btn-light <?php echo !$canModifyAsset ? 'disabled' : ''; ?>" data-bs-toggle="tooltip" title="<?php echo $transferTooltip; ?>">
                                                            <i class="fas fa-exchange-alt text-success"></i>
                                                        </a>
                                                        <?php
                                                        break;

                                                    case 'logistics':
                                                        // Logistics gets edit, transfer, and delete buttons
                                                        ?>
                                                        <a href="/choims/modules/assets/edit.php?id=<?php echo $asset['asset_id']; ?>&return_url=<?php echo urlencode($returnUrl); ?>" class="btn btn-sm btn-light <?php echo !$canModifyAsset ? 'disabled' : ''; ?>" data-bs-toggle="tooltip" title="<?php echo $editTooltip; ?>">
                                                            <i class="fas fa-edit text-primary"></i>
                                                        </a>
                                                        <a href="<?php echo !$canModifyAsset ? 'javascript:void(0);' : '/choims/modules/transfers/create.php?type=asset&asset_id=' . $asset['asset_id']; ?>" class="btn btn-sm btn-light <?php echo !$canModifyAsset ? 'disabled' : ''; ?>" data-bs-toggle="tooltip" title="<?php echo $transferTooltip; ?>">
                                                            <i class="fas fa-exchange-alt text-success"></i>
                                                        </a>
                                                        <button class="btn btn-sm btn-light delete-asset <?php echo $asset['has_active_transfer'] > 0 ? 'disabled' : ''; ?>" data-asset-id="<?php echo $asset['asset_id']; ?>" data-bs-toggle="<?php echo $asset['has_active_transfer'] > 0 ? '' : 'modal'; ?>" data-bs-target="<?php echo $asset['has_active_transfer'] > 0 ? '' : '#deleteAssetModal'; ?>" title="<?php echo $asset['has_active_transfer'] > 0 ? 'Cannot delete - Transfer in progress' : 'Delete Asset'; ?>">
                                                            <i class="fas fa-trash-alt text-danger"></i>
                                                        </button>
                                                        <?php
                                                        break;

                                                    case 'department':
                                                    case 'healthcenter':
                                                        // Department and Health Center only get transfer button
                                                        ?>
                                                        <a href="<?php echo !$canModifyAsset ? 'javascript:void(0);' : '/choims/modules/transfers/create.php?type=asset&asset_id=' . $asset['asset_id']; ?>" class="btn btn-sm btn-light <?php echo !$canModifyAsset ? 'disabled' : ''; ?>" data-bs-toggle="tooltip" title="<?php echo $transferTooltip; ?>">
                                                            <i class="fas fa-exchange-alt text-success"></i>
                                                        </a>
                                                        <?php
                                                        break;

                                                    case 'godmode':
                                                        // GodMode gets all buttons
                                                        ?>
                                                        <a href="/choims/modules/assets/edit.php?id=<?php echo $asset['asset_id']; ?>&return_url=<?php echo urlencode($returnUrl); ?>" class="btn btn-sm btn-light <?php echo !$canModifyAsset ? 'disabled' : ''; ?>" data-bs-toggle="tooltip" title="<?php echo $editTooltip; ?>">
                                                            <i class="fas fa-edit text-primary"></i>
                                                        </a>
                                                        <a href="<?php echo !$canModifyAsset ? 'javascript:void(0);' : '/choims/modules/transfers/create.php?type=asset&asset_id=' . $asset['asset_id']; ?>" class="btn btn-sm btn-light <?php echo !$canModifyAsset ? 'disabled' : ''; ?>" data-bs-toggle="tooltip" title="<?php echo $transferTooltip; ?>">
                                                            <i class="fas fa-exchange-alt text-success"></i>
                                                        </a>
                                                        <button class="btn btn-sm btn-light delete-asset <?php echo $asset['has_active_transfer'] > 0 ? 'disabled' : ''; ?>" data-asset-id="<?php echo $asset['asset_id']; ?>" data-bs-toggle="<?php echo $asset['has_active_transfer'] > 0 ? '' : 'modal'; ?>" data-bs-target="<?php echo $asset['has_active_transfer'] > 0 ? '' : '#deleteAssetModal'; ?>" title="<?php echo $asset['has_active_transfer'] > 0 ? 'Cannot delete - Transfer in progress' : 'Delete Asset'; ?>">
                                                            <i class="fas fa-trash-alt text-danger"></i>
                                                        </button>
                                                        <?php
                                                        break;

                                                    default:
                                                        // Other roles get no action buttons
                                                        break;
                                                }
                                                ?>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8">
                                    <div class="empty-state">
                                        <div class="empty-state-icon">
                                            <i class="fas fa-laptop-house"></i>
                                        </div>
                                        <h4 class="empty-state-title">No assets found</h4>
                                        <p class="empty-state-description">There are no fixed assets matching your current filters.</p>
                                        <?php if (hasRole('Logistics')): ?>
                                            <a href="/choims/modules/assets/add.php" class="btn btn-success">
                                                <i class="fas fa-plus me-2"></i> Add New Asset
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
                </div>
            </div>

            <!-- Locations View -->
            <div id="locationsView" style="display: none;">
                <?php if (hasRole('Logistics', 'HIMU', 'Superadmin', 'GodMode')): ?>
                <div class="locations-container">
                    <?php
                    // Calculate assets by location
                    mysqli_data_seek($result, 0);
                    $locations_count = [];
                    $locations_status = [];

                    while ($asset = mysqli_fetch_assoc($result)) {
                        if (!isset($locations_count[$asset['location_name']])) {
                            $locations_count[$asset['location_name']] = 0;
                            $locations_status[$asset['location_name']] = [
                                'In use' => 0,
                                'Available' => 0,
                                'Under Repair' => 0,
                                'Defective' => 0
                            ];
                        }
                        $locations_count[$asset['location_name']]++;
                        $locations_status[$asset['location_name']][$asset['status']]++;
                    }

                    // Reset the result pointer for later use
                    mysqli_data_seek($result, 0);

                    // Sort locations by asset count (descending)
                    arsort($locations_count);

                    // Get the user's current location
                    $userLocationName = '';
                    $userLocationId = isset($_SESSION['location_id']) ? $_SESSION['location_id'] : null;

                    if ($userLocationId) {
                        // Get the location name for the user's location ID
                        $userLocationQuery = "SELECT location_name FROM locations WHERE location_id = ?";
                        $userLocationStmt = mysqli_prepare($conn, $userLocationQuery);
                        mysqli_stmt_bind_param($userLocationStmt, 'i', $userLocationId);
                        mysqli_stmt_execute($userLocationStmt);
                        $userLocationResult = mysqli_stmt_get_result($userLocationStmt);

                        if ($userLocationRow = mysqli_fetch_assoc($userLocationResult)) {
                            $userLocationName = $userLocationRow['location_name'];
                        }
                    }
                    ?>

                    <div class="row">
                        <?php foreach ($locations_count as $location => $count):
                            $isUserLocation = ($location == $userLocationName);
                        ?>
                            <div class="col-md-4 col-sm-6 mb-3">
                                <div class="location-card p-3 rounded-3 border <?php echo $isUserLocation ? 'user-location' : ''; ?>">
                                    <?php if ($isUserLocation): ?>
                                    <div class="user-location-badge">Your Location</div>
                                    <?php endif; ?>
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="location-icon rounded-circle p-2 me-3 <?php echo $isUserLocation ? 'bg-success-bg' : 'bg-primary-bg'; ?>">
                                            <i class="fas fa-map-marker-alt <?php echo $isUserLocation ? 'text-success' : 'text-primary'; ?>"></i>
                                        </div>
                                        <h6 class="mb-0"><?php echo $location; ?></h6>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <div class="text-muted small">Total Assets: <span class="fw-bold text-dark"><?php echo $count; ?></span></div>
                                        </div>
                                        <div class="badge <?php echo $isUserLocation ? 'bg-success-bg text-success' : 'bg-primary-bg text-primary'; ?> px-3 py-2 rounded-pill">
                                            <?php echo round(($count / $total_assets) * 100); ?>%
                                        </div>
                                    </div>
                                    <div class="progress mb-2" style="height: 4px;">
                                        <div class="progress-bar <?php echo $isUserLocation ? 'bg-success' : 'bg-primary'; ?>" role="progressbar" style="width: <?php echo ($total_assets > 0) ? ($count / $total_assets * 100) : 0; ?>%" aria-valuenow="<?php echo ($total_assets > 0) ? ($count / $total_assets * 100) : 0; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="d-flex flex-wrap gap-1 mt-2">
                                        <?php if ($locations_status[$location]['In use'] > 0): ?>
                                            <span class="badge bg-primary bg-opacity-10 text-primary">
                                                <i class="fas fa-user me-1"></i> In use: <?php echo $locations_status[$location]['In use']; ?>
                                            </span>
                                        <?php endif; ?>
                                        <?php if ($locations_status[$location]['Available'] > 0): ?>
                                            <span class="badge bg-success bg-opacity-10 text-success">
                                                <i class="fas fa-check-circle me-1"></i> Available: <?php echo $locations_status[$location]['Available']; ?>
                                            </span>
                                        <?php endif; ?>
                                        <?php if ($locations_status[$location]['Under Repair'] > 0): ?>
                                            <span class="badge bg-warning bg-opacity-10 text-warning">
                                                <i class="fas fa-tools me-1"></i> Repair: <?php echo $locations_status[$location]['Under Repair']; ?>
                                            </span>
                                        <?php endif; ?>
                                        <?php if ($locations_status[$location]['Defective'] > 0): ?>
                                            <span class="badge bg-danger bg-opacity-10 text-danger">
                                                <i class="fas fa-exclamation-triangle me-1"></i> Defective: <?php echo $locations_status[$location]['Defective']; ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    You don't have permission to view location breakdown. Please contact your administrator.
                </div>
                <?php endif; ?>
            </div>

            <?php if (hasRole('Logistics') || hasRole('Superadmin')): ?>
            <div class="mt-4 d-flex gap-2 justify-content-end">
                <div class="dropdown">
                    <button class="btn btn-light dropdown-toggle btn-rounded" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-download text-success me-1"></i> Export
                    </button>
                    <ul class="dropdown-menu shadow" aria-labelledby="exportDropdown">
                        <li>
                            <button id="exportExcel" class="dropdown-item">
                                <i class="fas fa-file-excel text-success me-2"></i> Export to Excel
                            </button>
                        </li>
                        <li>
                            <button id="printButton" class="dropdown-item">
                                <i class="fas fa-print text-success me-2"></i> Print Current Page
                            </button>
                        </li>
                        <li>
                            <button id="printAllAssets" class="dropdown-item">
                                <i class="fas fa-print text-primary me-2"></i> Print All Assets
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add DataTables Buttons and Excel Export libraries -->
<script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.3/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.3/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.3/js/buttons.print.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // View toggle functionality
    const itemsViewBtn = document.getElementById('itemsViewBtn');
    const locationsViewBtn = document.getElementById('locationsViewBtn');
    const itemsView = document.getElementById('itemsView');
    const locationsView = document.getElementById('locationsView');
    const filterSection = document.getElementById('filterSection');

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            delay: { show: 0, hide: 0 },
            container: 'body'
        });
    });

    // Set up view toggle event listeners
    itemsViewBtn.addEventListener('click', function() {
        itemsViewBtn.classList.add('active');
        locationsViewBtn.classList.remove('active');
        itemsView.style.display = 'block';
        locationsView.style.display = 'none';
        filterSection.style.display = 'block';

        // Adjust DataTable columns if it exists
        if (typeof table !== 'undefined' && table.columns) {
            table.columns.adjust().draw();
        }

        // Save preference to localStorage
        localStorage.setItem('assetsViewPreference', 'items');
    });

    locationsViewBtn.addEventListener('click', function() {
        locationsViewBtn.classList.add('active');
        itemsViewBtn.classList.remove('active');
        locationsView.style.display = 'block';
        itemsView.style.display = 'none';
        filterSection.style.display = 'none';

        // Save preference to localStorage
        localStorage.setItem('assetsViewPreference', 'locations');
    });

    // Check for saved preference
    const savedViewPreference = localStorage.getItem('assetsViewPreference');
    if (savedViewPreference === 'locations') {
        locationsViewBtn.click();
    }

    // Initialize DataTable with modern styling
    var table = $('#assetsTable').DataTable({
        "pageLength": 25,
        "order": [[7, 'desc']],  // Default sort by Added Date column (index 7) in descending order
        "lengthChange": true,
        "autoWidth": false,
        "responsive": true,
        "stateSave": true,
        "columnDefs": [
            {
                // Disable sorting and searching for checkbox column
                "targets": 0,
                "orderable": false,
                "searchable": false
            },
            {
                // Use the data-sort attribute for the Added Date column
                "targets": 7,
                "orderData": 7,
                "type": "num"
            }
        ],
        "language": {
            "search": "<i class='fas fa-search'></i> ",
            "searchPlaceholder": "Quick search...",
            "lengthMenu": "<i class='fas fa-list-ol'></i> _MENU_ per page",
            "zeroRecords": "<i class='fas fa-info-circle'></i> No matching assets found",
            "info": "<span class='text-success'><i class='fas fa-clipboard-list'></i> Showing _START_ to _END_ of _TOTAL_ assets</span>",
            "infoEmpty": "<i class='fas fa-folder-open'></i> No assets available",
            "infoFiltered": "<small>(filtered from _MAX_ assets)</small>",
            "paginate": {
                "first": "<i class='fas fa-angle-double-left'></i>",
                "last": "<i class='fas fa-angle-double-right'></i>",
                "next": "<i class='fas fa-angle-right'></i>",
                "previous": "<i class='fas fa-angle-left'></i>"
            }
        },
        "initComplete": function(settings, json) {
            // Add custom styling to the DataTables elements
            $('.dataTables_wrapper .dataTables_filter input').addClass('form-control form-control-sm ms-2').css('width', '200px');
            $('.dataTables_wrapper .dataTables_length select').addClass('form-select form-select-sm');
            $('.dataTables_info').addClass('text-muted small pt-3');

            // Always hide loading state and show the table regardless of data
            if(document.getElementById('tableLoadingState')) {
                document.getElementById('tableLoadingState').style.display = 'none';
            }
            if(document.getElementById('assetsTable')) {
                document.getElementById('assetsTable').style.display = 'table';
            }

            // Make sure the table is visible with proper width
            if (table && table.columns) {
                table.columns.adjust().draw();
            }
        },
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'copy',
                className: 'd-none'
            },
            {
                extend: 'excel',
                className: 'd-none',
                title: 'Fixed Assets List',
                orientation: 'landscape',
                pageSize: 'A4',
                exportOptions: {
                    columns: [2, 3, 4, 5, 6, 7], // Export all columns except checkbox and Actions
                    stripHtml: true
                },
                customize: function(xlsx) {
                    // Add styling to Excel export
                    var sheet = xlsx.xl.worksheets['sheet1.xml'];
                    $('row:first c', sheet).attr('s', '32'); // Add header style
                }
            },
            {
                extend: 'print',
                className: 'd-none',
                title: 'Fixed Assets List',
                messageTop: null,
                messageBottom: null,
                autoPrint: true,
                orientation: 'landscape',
                exportOptions: {
                    columns: [2, 3, 4, 5, 6, 7], // Print all columns except checkbox and Actions
                    stripHtml: false,
                    modifier: {
                        page: 'current' // Only print the current page
                    }
                },
                customize: function (win) {
                    // Remove any default headers/footers
                    $(win.document.body).find('h1').remove();
                    $(win.document.body).find('div.dt-print-heading').remove();

                    // Set document title to avoid about:blank
                    $(win.document).attr('title', 'Fixed Assets List');

                    // Add padding to the page
                    $(win.document.body).css({
                        'padding-left': '30px',
                        'padding-right': '30px',
                        'padding-top': '20px',
                        'padding-bottom': '20px'
                    });

                    // Get current user and formatted date for print header
                    var currentUser = '<?php echo isset($_SESSION["username"]) ? $_SESSION["username"] : "User"; ?>';
                    var currentDate = new Date().toLocaleDateString('en-US', {year: 'numeric', month: 'short', day: 'numeric'});
                    var currentTime = new Date().toLocaleTimeString('en-US', {hour: '2-digit', minute: '2-digit'});

                    // Get filter information
                    var filterInfo = '';
                    var categoryFilter = '<?php echo !empty($category_id) ? mysqli_fetch_assoc(mysqli_query($conn, "SELECT category_name FROM categories WHERE category_id = $category_id"))["category_name"] : "All Categories"; ?>';
                    var locationFilter = '<?php echo !empty($location_id) ? mysqli_fetch_assoc(mysqli_query($conn, "SELECT location_name FROM locations WHERE location_id = $location_id"))["location_name"] : "All Locations"; ?>';
                    var statusFilter = '<?php echo !empty($status) ? $status : "All Statuses"; ?>';
                    var searchFilter = '<?php echo !empty($search) ? "Search: " . $search : ""; ?>';

                    if (categoryFilter !== 'All Categories' || locationFilter !== 'All Locations' || statusFilter !== 'All Statuses' || searchFilter !== '') {
                        filterInfo = '<div style="margin-top:10px; font-size:12px; color:#555;">';
                        filterInfo += '<strong>Filters:</strong> ';
                        filterInfo += categoryFilter !== 'All Categories' ? 'Category: ' + categoryFilter + ' | ' : '';
                        filterInfo += locationFilter !== 'All Locations' ? 'Location: ' + locationFilter + ' | ' : '';
                        filterInfo += statusFilter !== 'All Statuses' ? 'Status: ' + statusFilter + ' | ' : '';
                        filterInfo += searchFilter !== '' ? searchFilter : '';
                        filterInfo += '</div>';
                    }

                    // Get summary information
                    var totalAssets = '<?php echo $total_assets; ?>';
                    var inUseAssets = '<?php echo $in_use_assets; ?>';
                    var availableAssets = '<?php echo $available_assets; ?>';
                    var underRepairAssets = '<?php echo $under_repair_assets; ?>';
                    var defectiveAssets = '<?php echo $defective_assets; ?>';

                    var summaryInfo = '<div style="margin-top:15px; margin-bottom:15px; padding:10px; background-color:#f8f9fa; border-radius:5px; font-size:12px;">';
                    summaryInfo += '<strong>Summary:</strong> ';
                    summaryInfo += 'Total: ' + totalAssets + ' | ';
                    summaryInfo += 'In Use: ' + inUseAssets + ' | ';
                    summaryInfo += 'Available: ' + availableAssets + ' | ';
                    summaryInfo += 'Under Repair: ' + underRepairAssets + ' | ';
                    summaryInfo += 'Defective: ' + defectiveAssets;
                    summaryInfo += '</div>';

                    // Add logo, title, and user info with date
                    $(win.document.body).prepend(
                        '<div style="display:flex; align-items:center; justify-content:space-between; margin-bottom:10px;">' +
                        '<div style="display:flex; align-items:center;">' +
                        '<img src="/choims/assets/img/prqlogo2.png" style="height:80px; margin-right:20px;" />' +
                        '<div>' +
                        '<h1 style="margin:0; color:#2e7d32; font-weight:bold;">Fixed Assets List</h1>' +
                        filterInfo +
                        '</div>' +
                        '</div>' +
                        '<div style="text-align:right; color:#2e7d32; font-size:14px; font-weight:500;">' +
                        '<div style="margin-bottom:5px;"><span style="display:inline-block; width:80px;">Printed by:</span>&nbsp;&nbsp;' + currentUser + '</div>' +
                        '<div style="margin-bottom:5px;"><span style="display:inline-block; width:80px;">Date:</span>&nbsp;&nbsp;' + currentDate + '</div>' +
                        '<div><span style="display:inline-block; width:80px;">Time:</span>&nbsp;&nbsp;' + currentTime + '</div>' +
                        '</div>' +
                        '</div>' +
                        summaryInfo
                    );

                    // Clean up print view and remove date/about:blank
                    $(win.document.head).append(`
                        <style>
                            @page {
                                size: landscape;
                                margin: 0mm;
                            }
                            body {
                                margin: 0;
                                padding: 1.6cm;
                                -webkit-print-color-adjust: exact !important;
                                print-color-adjust: exact !important;
                                color-adjust: exact !important;
                            }
                            table {
                                margin-top: 15px;
                                width: 100%;
                                border-collapse: collapse;
                                font-size: 12px;
                            }
                            table th {
                                background-color: #f8f9fa !important;
                                color: #333 !important;
                                font-weight: bold;
                                padding: 8px;
                                border: 1px solid #ddd;
                                text-align: left;
                            }
                            table td {
                                padding: 8px;
                                border: 1px solid #ddd;
                                vertical-align: middle;
                            }
                            table tr:nth-child(even) {
                                background-color: #f9f9f9 !important;
                            }
                            /* Add spacing after page breaks */
                            @media print {
                                @page :footer {
                                    display: none
                                }
                                @page :header {
                                    display: none
                                }
                                /* First page has no margin, other pages have 20px margin */
                                @page :first {
                                    margin-top: 0mm;
                                }
                                @page {
                                    margin-top: 20px;
                                }
                                tr {
                                    page-break-inside: avoid;
                                }
                                thead {
                                    display: table-header-group;
                                }
                                tfoot {
                                    display: table-footer-group;
                                }
                                /* Add margin to content after page break */
                                .pagebreak {
                                    page-break-before: always;
                                    margin-top: 20px;
                                }
                                tr.pagebreak {
                                    margin-top: 20px;
                                }
                                /* Add page numbers */
                                .page-number:after {
                                    content: counter(page);
                                }
                                /* Footer with page numbers */
                                #footer {
                                    position: fixed;
                                    bottom: 0;
                                    width: 100%;
                                    text-align: center;
                                    font-size: 10px;
                                    color: #777;
                                    padding: 5px 0;
                                }
                            }
                        </style>
                    `);

                    // Add footer with page numbers
                    $(win.document.body).append('<div id="footer">Page <span class="page-number"></span></div>');

                    // Keep the table styling intact
                    $(win.document.body).find('table')
                        .addClass('table table-bordered')
                        .css('font-size', '12px');

                    // Ensure status badges retain their colors in print
                    $(win.document.body).find('.badge').each(function() {
                        var background = '';

                        if ($(this).text().trim() === 'In use') background = '#0d6efd';
                        if ($(this).text().trim() === 'Available') background = '#198754';
                        if ($(this).text().trim() === 'Under Repair') background = '#ffc107';
                        if ($(this).text().trim() === 'Defective') background = '#dc3545';

                        $(this).css({
                            'background-color': background,
                            'color': '#fff',
                            'padding': '3px 8px',
                            'border-radius': '12px',
                            'font-size': '11px',
                            'font-weight': 'bold'
                        });
                    });
                }
            }
        ]
    });

    // Export to Excel button
    $('#exportExcel').on('click', function() {
        if (table && table.button) {
            table.button('.buttons-excel').trigger();
        } else {
            console.error('DataTable or button method not available');
        }
    });

    // Print buttons (both in header and dropdown) - print only current page
    $('#printButton, #printTable').on('click', function() {
        if (table) {
            // Get current page info
            const info = table.page.info();
            const currentLength = table.page.len();
            const currentStart = info.start;

            // Store current page and length settings
            const currentPageIndex = table.page();

            // Set page length to match the current page's data
            table.page.len(info.length).draw(false);

            // Go to the current page
            table.page(currentPageIndex).draw(false);

            // Trigger print
            setTimeout(function() {
                if (table.button) {
                    table.button('.buttons-print').trigger();
                } else {
                    window.print();
                }

                // Restore original settings after printing
                setTimeout(function() {
                    table.page.len(currentLength).draw(false);
                    table.page(currentPageIndex).draw(false);
                }, 1000);
            }, 500);
        } else {
            console.error('DataTable not available');
        }
    });

    // Print All Assets button (prints all assets without pagination)
    $('#printAllAssets').on('click', function() {
        if (table) {
            // Save current page length and page
            const currentLength = table.page.len();
            const currentPage = table.page();

            // Create a custom print button that prints all pages
            const printAllButton = {
                extend: 'print',
                title: 'Fixed Assets List - All Items',
                messageTop: '<div style="text-align: center; font-size: 14px; margin-bottom: 10px;"><strong>Complete List</strong> - Showing all assets</div>',
                exportOptions: {
                    columns: [2, 3, 4, 5, 6, 7],
                    stripHtml: false,
                    modifier: {
                        page: 'all' // Print all pages
                    }
                },
                customize: function(win) {
                    // Apply the same customization as the regular print button
                    $(win.document.body).find('h1').remove();
                    $(win.document.body).find('div.dt-print-heading').remove();

                    // Set document title
                    $(win.document).attr('title', 'Fixed Assets List - All Items');

                    // Add styling
                    $(win.document.head).append(`
                        <style>
                            @page { size: landscape; }
                            body { padding: 1.6cm; }
                            table { width: 100%; border-collapse: collapse; }
                            table th { background-color: #f8f9fa !important; }
                            table td, table th { border: 1px solid #ddd; padding: 8px; }
                        </style>
                    `);
                }
            };

            // Set page length to show all records
            table.page.len(-1).draw();

            // Trigger custom print
            setTimeout(function() {
                $.fn.dataTable.ext.buttons.print.action.call(
                    table.button(null).node(),
                    table.button(null).config()[0],
                    printAllButton
                );

                // Restore original page length and page after printing
                setTimeout(function() {
                    table.page.len(currentLength).draw();
                    table.page(currentPage).draw();
                }, 1000);
            }, 500);
        } else {
            console.error('DataTable not available');
        }
    });

    // Add indicator when filtering is active
    const filterButton = document.querySelector('[data-bs-toggle="collapse"]');
    const filterCollapse = document.getElementById('filterCollapse');

    if (filterButton && filterCollapse) {
        filterCollapse.addEventListener('shown.bs.collapse', function () {
            filterButton.querySelector('i').classList.remove('fa-filter');
            filterButton.querySelector('i').classList.add('fa-filter-circle-xmark');
        });

        filterCollapse.addEventListener('hidden.bs.collapse', function () {
            filterButton.querySelector('i').classList.remove('fa-filter-circle-xmark');
            filterButton.querySelector('i').classList.add('fa-filter');
        });
    }

    // Set asset ID for deletion (only if delete buttons exist)
    const deleteButtons = document.querySelectorAll('.delete-asset');
    if (deleteButtons.length > 0) {
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                document.getElementById('deleteAssetId').value = this.getAttribute('data-asset-id');
            });
        });
    }

    // Set asset ID for restoration (only if restore buttons exist)
    const restoreButtons = document.querySelectorAll('.restore-asset');
    if (restoreButtons.length > 0) {
        restoreButtons.forEach(button => {
            button.addEventListener('click', function() {
                document.getElementById('restoreAssetId').value = this.getAttribute('data-asset-id');
            });
        });
    }

    // Add styles to datatable pagination
    function styleDataTablePagination() {
        $('.dataTables_paginate .paginate_button').addClass('btn btn-sm');
        $('.dataTables_paginate .paginate_button.current').addClass('btn-success').removeClass('btn-sm');
        $('.dataTables_paginate .paginate_button:not(.current)').addClass('btn-light');
    }

    // Style pagination on first load
    styleDataTablePagination();

    // Style pagination when changed
    table.on('draw', function() {
        styleDataTablePagination();

        // Reinitialize tooltips after table redraw
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl, {
                delay: { show: 300, hide: 100 },
                container: 'body'
            });
        });

        // Reinitialize batch transfer functionality after table redraw
        initializeBatchTransfer();
    });

    // Initialize batch transfer functionality
    function initializeBatchTransfer() {
        const selectAllCheckbox = document.getElementById('selectAll');
        const assetCheckboxes = document.querySelectorAll('.asset-checkbox');
        const batchTransferControls = document.getElementById('batchTransferControls');
        const selectedCountElement = document.getElementById('selectedCount');
        const clearSelectionBtn = document.getElementById('clearSelection');
        const batchTransferForm = document.getElementById('batchTransferForm');

        // Update selected count and show/hide controls
        function updateBatchControls() {
            const selectedCheckboxes = document.querySelectorAll('.asset-checkbox:checked');
            const selectedCount = selectedCheckboxes.length;

            selectedCountElement.textContent = selectedCount;

            if (selectedCount > 0) {
                batchTransferControls.style.display = 'block';

                // Remove existing hidden inputs
                const existingInputs = batchTransferForm.querySelectorAll('input[name="selected_assets[]"]');
                existingInputs.forEach(input => input.remove());

                // Add selected asset IDs to form
                selectedCheckboxes.forEach(checkbox => {
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'selected_assets[]';
                    hiddenInput.value = checkbox.value;
                    batchTransferForm.appendChild(hiddenInput);
                });
            } else {
                batchTransferControls.style.display = 'none';
            }

            // Update select all checkbox state
            if (selectAllCheckbox) {
                if (selectedCount === 0) {
                    selectAllCheckbox.indeterminate = false;
                    selectAllCheckbox.checked = false;
                } else if (selectedCount === assetCheckboxes.length) {
                    selectAllCheckbox.indeterminate = false;
                    selectAllCheckbox.checked = true;
                } else {
                    selectAllCheckbox.indeterminate = true;
                }
            }
        }

        // Select all functionality
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const isChecked = this.checked;
                assetCheckboxes.forEach(checkbox => {
                    checkbox.checked = isChecked;
                });
                updateBatchControls();
            });
        }

        // Individual checkbox functionality
        assetCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateBatchControls);
        });

        // Clear selection functionality
        if (clearSelectionBtn) {
            clearSelectionBtn.addEventListener('click', function() {
                assetCheckboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = false;
                    selectAllCheckbox.indeterminate = false;
                }
                updateBatchControls();
            });
        }

        // Form submission validation
        if (batchTransferForm) {
            batchTransferForm.addEventListener('submit', function(e) {
                const selectedCheckboxes = document.querySelectorAll('.asset-checkbox:checked');
                if (selectedCheckboxes.length === 0) {
                    e.preventDefault();
                    alert('Please select at least one asset to transfer.');
                    return false;
                }

                // Confirm batch transfer
                const assetNames = Array.from(selectedCheckboxes).map(cb => cb.getAttribute('data-asset-name')).slice(0, 3);
                let confirmMessage = `Are you sure you want to initiate batch transfer for ${selectedCheckboxes.length} asset(s)?`;
                if (assetNames.length > 0) {
                    confirmMessage += `\n\nSelected assets include: ${assetNames.join(', ')}`;
                    if (selectedCheckboxes.length > 3) {
                        confirmMessage += ` and ${selectedCheckboxes.length - 3} more...`;
                    }
                }

                if (!confirm(confirmMessage)) {
                    e.preventDefault();
                    return false;
                }
            });
        }
    }

    // Initialize batch transfer on page load
    initializeBatchTransfer();
});

// Add dismiss functionality to alerts
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        const closeButton = alert.querySelector('.btn-close');
        if (closeButton) {
            closeButton.addEventListener('click', function() {
                // Simply remove the alert without animation
                alert.remove();
            });
        }
    });
});
</script>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>

<!-- Delete Asset Modal -->
<div class="modal fade" id="deleteAssetModal" tabindex="-1" aria-labelledby="deleteAssetModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger bg-opacity-10 border-0">
                <h5 class="modal-title text-danger d-flex align-items-center" id="deleteAssetModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i> Confirm Deletion
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="text-center mb-4">
                    <div class="bg-danger bg-opacity-10 d-inline-flex p-3 rounded-circle mb-3">
                        <i class="fas fa-trash-alt fa-2x text-danger"></i>
                    </div>
                    <h5 class="mb-3">Delete Asset</h5>
                    <p class="text-muted">Are you sure you want to delete this asset?</p>
                </div>
            </div>
            <div class="modal-footer bg-light border-0 justify-content-center gap-2">
                <button type="button" class="btn btn-outline-secondary btn-rounded" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> Cancel
                </button>
                <form id="deleteAssetForm" action="/choims/modules/assets/delete.php" method="post" class="d-inline">
                    <input type="hidden" name="asset_id" id="deleteAssetId" value="">
                    <button type="submit" class="btn btn-danger btn-rounded">
                        <i class="fas fa-trash-alt me-1"></i> Delete
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Restore Asset Modal -->
<div class="modal fade" id="restoreAssetModal" tabindex="-1" aria-labelledby="restoreAssetModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-success bg-opacity-10 border-0">
                <h5 class="modal-title text-success d-flex align-items-center" id="restoreAssetModalLabel">
                    <i class="fas fa-sync-alt me-2"></i> Confirm Restoration
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="text-center mb-4">
                    <div class="bg-success bg-opacity-10 d-inline-flex p-3 rounded-circle mb-3">
                        <i class="fas fa-trash-restore fa-2x text-success"></i>
                    </div>
                    <h5 class="mb-3">Restore Asset</h5>
                    <p class="text-muted">Are you sure you want to restore this asset? It will be made available in the system again.</p>
                </div>
            </div>
            <div class="modal-footer bg-light border-0 justify-content-center gap-2">
                <button type="button" class="btn btn-outline-secondary btn-rounded" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> Cancel
                </button>
                <form id="restoreAssetForm" action="/choims/modules/assets/restore.php" method="post" class="d-inline">
                    <input type="hidden" name="asset_id" id="restoreAssetId" value="">
                    <button type="submit" class="btn btn-success btn-rounded">
                        <i class="fas fa-trash-restore me-1"></i> Restore Asset
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>