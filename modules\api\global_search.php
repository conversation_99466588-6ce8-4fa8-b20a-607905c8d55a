<?php
/**
 * Global Search API
 *
 * This endpoint provides a unified search across multiple entities in the system.
 * It respects user roles and permissions, ensuring users only see results they have access to.
 */

// Include necessary files
try {
    require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header_minimal.php');
    require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/auth.php');
    require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/functions.php');
} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'System error: ' . $e->getMessage()]);
    exit;
}

// Ensure user is logged in
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Authentication required']);
    exit;
}

// Get search term
$search = isset($_GET['q']) ? trim($_GET['q']) : '';

// Validate search term
if (empty($search) || strlen($search) < 2) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Search term must be at least 2 characters']);
    exit;
}

// Initialize results array
$results = [
    'assets' => [],
    'consumables' => [],
    'locations' => [],
    'transactions' => [],
    'batch_transfers' => [],
    'users' => [],
    'suppliers' => [],
    'maintenance' => [],
    'categories' => [],
    'skus' => [],
    'total' => 0
];

// Get user role and location
$userRole = $_SESSION['role'];
$userLocationId = isset($_SESSION['location_id']) ? $_SESSION['location_id'] : null;

// Prepare search parameter
$searchParam = "%$search%";

// For assigned_to field, create flexible search terms
$searchWords = explode(' ', trim($search));
$assignedToConditions = [];
$assignedToParams = [];

// Add individual word searches for assigned_to
foreach ($searchWords as $word) {
    if (!empty(trim($word))) {
        $assignedToConditions[] = "fa.assigned_to LIKE ?";
        $assignedToParams[] = "%$word%";
    }
}

// Add space-removed search for assigned_to
$spaceRemovedSearch = str_replace(' ', '', $search);
$assignedToConditions[] = "REPLACE(LOWER(fa.assigned_to), ' ', '') LIKE ?";
$assignedToParams[] = "%$spaceRemovedSearch%";

// Search fixed assets
$assetQuery = "
    SELECT
        fa.asset_id,
        fa.asset_name,
        fa.serial_number,
        s.sku_code,
        s.sku_name,
        c.category_name,
        l.location_name,
        fa.status
    FROM
        fixed_assets fa
    JOIN
        sku_master s ON fa.sku_id = s.sku_id
    JOIN
        categories c ON s.category_id = c.category_id
    JOIN
        locations l ON fa.current_location_id = l.location_id
    WHERE
        (fa.is_deleted = 0 OR fa.is_deleted IS NULL)
        AND (
            fa.asset_name LIKE ?
            OR fa.serial_number LIKE ?
            OR s.sku_name LIKE ?
            OR s.sku_code LIKE ?
            OR fa.remarks LIKE ?
            OR fa.local_mr LIKE ?
            " . (!empty($assignedToConditions) ? "OR (" . implode(" OR ", $assignedToConditions) . ")" : "") . "
        )
";

// Apply role-based restrictions
if (!hasRole('GodMode') && !hasRole('Superadmin') && !hasRole('Logistics')) {
    if (hasRole('HIMU')) {
        // Get HIMU location ID from the user's session
        $himuLocationId = isset($_SESSION['location_id']) ? $_SESSION['location_id'] : null;

        // If we don't have a location ID from the session, try to find it by name
        if (!$himuLocationId) {
            $himuLocationQuery = "SELECT location_id FROM locations WHERE location_name LIKE '%HIMU%' OR location_name LIKE '%IT%' LIMIT 1";
            $himuLocationResult = mysqli_query($conn, $himuLocationQuery);

            if ($himuLocationResult && mysqli_num_rows($himuLocationResult) > 0) {
                $himuLocationRow = mysqli_fetch_assoc($himuLocationResult);
                $himuLocationId = $himuLocationRow['location_id'];
            }
        }

        // For HIMU users:
        // 1. Show all IT Equipment (category_id = 1) regardless of location
        // 2. Only show Office Equipment and Medical Equipment in HIMU's location
        if ($himuLocationId) {
            $assetQuery .= " AND (c.category_id = 1 OR (c.category_id IN (2, 3) AND fa.current_location_id = $himuLocationId))";
        } else {
            // If we can't find HIMU's location, just show IT Equipment
            $assetQuery .= " AND c.category_id = 1";
        }
    } elseif ($userLocationId) {
        // Department and Health Center users can only see their location's assets
        $assetQuery .= " AND fa.current_location_id = $userLocationId";
    }
}

$assetQuery .= " LIMIT 5"; // Limit to 5 results for quick search

try {
    $assetStmt = mysqli_prepare($conn, $assetQuery);
    if (!$assetStmt) {
        throw new Exception("Prepare failed: " . mysqli_error($conn));
    }

    // Prepare all parameters
    $allParams = [$searchParam, $searchParam, $searchParam, $searchParam, $searchParam, $searchParam];
    $allParams = array_merge($allParams, $assignedToParams);

    // Create parameter type string
    $paramTypes = str_repeat('s', count($allParams));

    mysqli_stmt_bind_param($assetStmt, $paramTypes, ...$allParams);
    if (!mysqli_stmt_execute($assetStmt)) {
        throw new Exception("Execute failed: " . mysqli_stmt_error($assetStmt));
    }
    $assetResult = mysqli_stmt_get_result($assetStmt);
} catch (Exception $e) {
    // Log the error but continue with other searches
    error_log("Asset search error: " . $e->getMessage());
    $assetResult = false;
}

if ($assetResult) {
    while ($asset = mysqli_fetch_assoc($assetResult)) {
        $results['assets'][] = [
            'id' => $asset['asset_id'],
            'name' => $asset['asset_name'],
            'sku' => $asset['sku_code'],
            'category' => $asset['category_name'],
            'location' => $asset['location_name'],
            'status' => $asset['status'],
            'url' => "/choims/modules/assets/view.php?id=" . $asset['asset_id']
        ];
    }
}

// Search consumables/inventory
$inventoryQuery = "
    SELECT
        i.inventory_id,
        s.sku_code,
        s.sku_name,
        c.category_name,
        l.location_name,
        i.current_quantity as quantity,
        i.status
    FROM
        consumable_inventory i
    JOIN
        sku_master s ON i.sku_id = s.sku_id
    JOIN
        categories c ON s.category_id = c.category_id
    JOIN
        locations l ON i.location_id = l.location_id
    WHERE
        (i.is_deleted = 0 OR i.is_deleted IS NULL)
        AND (
            s.sku_name LIKE ?
            OR s.sku_code LIKE ?
            OR s.description LIKE ?
        )
";

// Apply role-based restrictions
if (!hasRole('GodMode') && !hasRole('Superadmin') && !hasRole('Logistics')) {
    if (hasRole('HIMU')) {
        // HIMU can see all IT equipment
        $inventoryQuery .= " AND c.category_id = 1"; // Assuming category_id 1 is IT Equipment
    } elseif ($userLocationId) {
        // Department and Health Center users can only see their location's inventory
        $inventoryQuery .= " AND i.location_id = $userLocationId";
    }
}

$inventoryQuery .= " LIMIT 5"; // Limit to 5 results for quick search

try {
    $inventoryStmt = mysqli_prepare($conn, $inventoryQuery);
    if (!$inventoryStmt) {
        throw new Exception("Prepare failed: " . mysqli_error($conn));
    }
    mysqli_stmt_bind_param($inventoryStmt, 'sss', $searchParam, $searchParam, $searchParam);
    if (!mysqli_stmt_execute($inventoryStmt)) {
        throw new Exception("Execute failed: " . mysqli_stmt_error($inventoryStmt));
    }
    $inventoryResult = mysqli_stmt_get_result($inventoryStmt);
} catch (Exception $e) {
    // Log the error but continue with other searches
    error_log("Inventory search error: " . $e->getMessage());
    $inventoryResult = false;
}

if ($inventoryResult) {
    while ($item = mysqli_fetch_assoc($inventoryResult)) {
        $results['consumables'][] = [
            'id' => $item['inventory_id'],
            'sku' => $item['sku_code'],
            'name' => $item['sku_name'],
            'category' => $item['category_name'],
            'location' => $item['location_name'],
            'quantity' => $item['quantity'],
            'status' => $item['status'],
            'url' => "/choims/modules/inventory/view.php?id=" . $item['inventory_id']
        ];
    }
}

// Search locations (if user has appropriate permissions)
if (hasRole('GodMode') || hasRole('Superadmin') || hasRole('Logistics') || hasRole('HIMU')) {
    $locationQuery = "
        SELECT
            location_id,
            location_name,
            location_type,
            address
        FROM
            locations
        WHERE
            location_name LIKE ?
        LIMIT 5
    ";

    try {
        $locationStmt = mysqli_prepare($conn, $locationQuery);
        if (!$locationStmt) {
            throw new Exception("Prepare failed: " . mysqli_error($conn));
        }
        mysqli_stmt_bind_param($locationStmt, 's', $searchParam);
        if (!mysqli_stmt_execute($locationStmt)) {
            throw new Exception("Execute failed: " . mysqli_stmt_error($locationStmt));
        }
        $locationResult = mysqli_stmt_get_result($locationStmt);

        if ($locationResult) {
            while ($location = mysqli_fetch_assoc($locationResult)) {
                $results['locations'][] = [
                    'id' => $location['location_id'],
                    'name' => $location['location_name'],
                    'type' => $location['location_type'],
                    'address' => $location['address'],
                    'url' => "/choims/modules/locations/view.php?id=" . $location['location_id']
                ];
            }
        }
    } catch (Exception $e) {
        // Log the error but continue
        error_log("Location search error: " . $e->getMessage());
    }
}

// Search for transaction codes in individual transfers
if (hasRole('GodMode') || hasRole('Superadmin') || hasRole('Logistics') || hasRole('HIMU') || hasRole('Department') || hasRole('HealthCenter')) {
    $transferQuery = "
        SELECT
            t.transfer_id,
            t.transaction_code,
            t.status,
            t.transfer_date,
            sl.location_name as source_location,
            dl.location_name as destination_location,
            CASE
                WHEN t.asset_id IS NOT NULL THEN CONCAT(sm.sku_code, ' - ', COALESCE(fa.asset_name, sm.sku_name))
                WHEN t.inventory_id IS NOT NULL THEN CONCAT(sm2.sku_code, ' - ', sm2.sku_name)
                ELSE 'Unknown Item'
            END as item_name,
            t.quantity
        FROM
            transfers t
        LEFT JOIN
            locations sl ON t.source_location_id = sl.location_id
        LEFT JOIN
            locations dl ON t.destination_location_id = dl.location_id
        LEFT JOIN
            fixed_assets fa ON t.asset_id = fa.asset_id
        LEFT JOIN
            sku_master sm ON fa.sku_id = sm.sku_id
        LEFT JOIN
            consumable_inventory ci ON t.inventory_id = ci.inventory_id
        LEFT JOIN
            sku_master sm2 ON ci.sku_id = sm2.sku_id
        WHERE
            t.transaction_code LIKE ?
    ";

    // Apply role-based restrictions
    if (!hasRole('GodMode') && !hasRole('Superadmin') && !hasRole('Logistics')) {
        if ($userLocationId) {
            // Users can only see transfers related to their location
            $transferQuery .= " AND (t.source_location_id = $userLocationId OR t.destination_location_id = $userLocationId)";
        }
    }

    $transferQuery .= " ORDER BY t.transfer_date DESC LIMIT 5";

    try {
        $transferStmt = mysqli_prepare($conn, $transferQuery);
        if (!$transferStmt) {
            throw new Exception("Prepare failed: " . mysqli_error($conn));
        }
        mysqli_stmt_bind_param($transferStmt, 's', $searchParam);
        if (!mysqli_stmt_execute($transferStmt)) {
            throw new Exception("Execute failed: " . mysqli_stmt_error($transferStmt));
        }
        $transferResult = mysqli_stmt_get_result($transferStmt);

        if ($transferResult) {
            while ($transfer = mysqli_fetch_assoc($transferResult)) {
                $results['transactions'][] = [
                    'id' => $transfer['transfer_id'],
                    'code' => $transfer['transaction_code'],
                    'status' => $transfer['status'],
                    'date' => $transfer['transfer_date'],
                    'source' => $transfer['source_location'],
                    'destination' => $transfer['destination_location'],
                    'item' => $transfer['item_name'],
                    'quantity' => $transfer['quantity'],
                    'url' => "/choims/modules/transfers/view.php?id=" . $transfer['transfer_id']
                ];
            }
        }
    } catch (Exception $e) {
        // Log the error but continue
        error_log("Transfer search error: " . $e->getMessage());
    }
}

// Search for batch transfers
if (hasRole('GodMode') || hasRole('Superadmin') || hasRole('Logistics') || hasRole('HIMU') || hasRole('Department') || hasRole('HealthCenter')) {
    $batchQuery = "
        SELECT
            b.batch_id,
            b.transaction_code,
            b.status,
            b.transfer_date,
            sl.location_name as source_location,
            dl.location_name as destination_location,
            CONCAT(COUNT(DISTINCT ba.asset_id), ' assets, ', COUNT(DISTINCT bi.inventory_id), ' inventory items') as item_summary
        FROM
            batch_transfers b
        LEFT JOIN
            locations sl ON b.source_location_id = sl.location_id
        LEFT JOIN
            locations dl ON b.destination_location_id = dl.location_id
        LEFT JOIN
            batch_transfer_assets ba ON b.batch_id = ba.batch_id
        LEFT JOIN
            batch_transfer_inventory bi ON b.batch_id = bi.batch_id
        WHERE
            b.transaction_code LIKE ?
    ";

    // Apply role-based restrictions
    if (!hasRole('GodMode') && !hasRole('Superadmin') && !hasRole('Logistics')) {
        if ($userLocationId) {
            // Users can only see batch transfers related to their location
            $batchQuery .= " AND (b.source_location_id = $userLocationId OR b.destination_location_id = $userLocationId)";
        }
    }

    $batchQuery .= " GROUP BY b.batch_id ORDER BY b.transfer_date DESC LIMIT 5";

    try {
        $batchStmt = mysqli_prepare($conn, $batchQuery);
        if (!$batchStmt) {
            throw new Exception("Prepare failed: " . mysqli_error($conn));
        }
        mysqli_stmt_bind_param($batchStmt, 's', $searchParam);
        if (!mysqli_stmt_execute($batchStmt)) {
            throw new Exception("Execute failed: " . mysqli_stmt_error($batchStmt));
        }
        $batchResult = mysqli_stmt_get_result($batchStmt);

        if ($batchResult) {
            while ($batch = mysqli_fetch_assoc($batchResult)) {
                $results['batch_transfers'][] = [
                    'id' => $batch['batch_id'],
                    'code' => $batch['transaction_code'],
                    'status' => $batch['status'],
                    'date' => $batch['transfer_date'],
                    'source' => $batch['source_location'],
                    'destination' => $batch['destination_location'],
                    'item_summary' => $batch['item_summary'],
                    'url' => "/choims/modules/transfers/batch/view.php?id=" . $batch['batch_id']
                ];
            }
        }
    } catch (Exception $e) {
        // Log the error but continue
        error_log("Batch transfer search error: " . $e->getMessage());
    }
}

// Search users (if user has appropriate permissions)
if (hasRole('GodMode') || hasRole('Superadmin') || hasRole('Logistics') || hasRole('HIMU')) {
    $userQuery = "
        SELECT
            u.user_id,
            u.username,
            u.full_name,
            u.email,
            u.role,
            l.location_name,
            u.is_active
        FROM
            users u
        LEFT JOIN
            locations l ON u.location_id = l.location_id
        WHERE
            u.is_active = 1
            AND (
                u.full_name LIKE ?
                OR u.username LIKE ?
                OR u.email LIKE ?
            )
        ORDER BY u.full_name
        LIMIT 5
    ";

    try {
        $userStmt = mysqli_prepare($conn, $userQuery);
        if (!$userStmt) {
            throw new Exception("Prepare failed: " . mysqli_error($conn));
        }
        mysqli_stmt_bind_param($userStmt, 'sss', $searchParam, $searchParam, $searchParam);
        if (!mysqli_stmt_execute($userStmt)) {
            throw new Exception("Execute failed: " . mysqli_stmt_error($userStmt));
        }
        $userResult = mysqli_stmt_get_result($userStmt);

        if ($userResult) {
            while ($user = mysqli_fetch_assoc($userResult)) {
                $results['users'][] = [
                    'id' => $user['user_id'],
                    'name' => $user['full_name'],
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'role' => $user['role'],
                    'location' => $user['location_name'],
                    'url' => "/choims/modules/users/profile.php?id=" . $user['user_id']
                ];
            }
        }
    } catch (Exception $e) {
        // Log the error but continue
        error_log("User search error: " . $e->getMessage());
    }
}

// Search suppliers
$supplierQuery = "
    SELECT
        supplier_id,
        supplier_name,
        supplier_code,
        contact_person,
        contact_number,
        address
    FROM
        suppliers
    WHERE
        (is_deleted = 0 OR is_deleted IS NULL)
        AND (
            supplier_name LIKE ?
            OR supplier_code LIKE ?
            OR contact_person LIKE ?
        )
    ORDER BY supplier_name
    LIMIT 5
";

try {
    $supplierStmt = mysqli_prepare($conn, $supplierQuery);
    if (!$supplierStmt) {
        throw new Exception("Prepare failed: " . mysqli_error($conn));
    }
    mysqli_stmt_bind_param($supplierStmt, 'sss', $searchParam, $searchParam, $searchParam);
    if (!mysqli_stmt_execute($supplierStmt)) {
        throw new Exception("Execute failed: " . mysqli_stmt_error($supplierStmt));
    }
    $supplierResult = mysqli_stmt_get_result($supplierStmt);

    if ($supplierResult) {
        while ($supplier = mysqli_fetch_assoc($supplierResult)) {
            $results['suppliers'][] = [
                'id' => $supplier['supplier_id'],
                'name' => $supplier['supplier_name'],
                'code' => $supplier['supplier_code'],
                'contact_person' => $supplier['contact_person'],
                'contact_number' => $supplier['contact_number'],
                'address' => $supplier['address'],
                'url' => "/choims/modules/admin/suppliers.php?id=" . $supplier['supplier_id']
            ];
        }
    }
} catch (Exception $e) {
    // Log the error but continue
    error_log("Supplier search error: " . $e->getMessage());
}

// Search maintenance records
$maintenanceQuery = "
    SELECT
        mr.record_id,
        mr.asset_id,
        mr.maintenance_type,
        mr.performed_by,
        mr.maintenance_date,
        mr.description,
        mr.status,
        fa.asset_name,
        s.sku_code,
        l.location_name
    FROM
        maintenance_records mr
    JOIN
        fixed_assets fa ON mr.asset_id = fa.asset_id
    JOIN
        sku_master s ON fa.sku_id = s.sku_id
    JOIN
        locations l ON fa.current_location_id = l.location_id
    WHERE
        (fa.is_deleted = 0 OR fa.is_deleted IS NULL)
        AND (
            mr.description LIKE ?
            OR mr.performed_by LIKE ?
            OR mr.maintenance_type LIKE ?
            OR fa.asset_name LIKE ?
        )
";

// Apply role-based restrictions
if (!hasRole('GodMode') && !hasRole('Superadmin') && !hasRole('Logistics')) {
    if (hasRole('HIMU')) {
        // HIMU can see maintenance for IT equipment
        $maintenanceQuery .= " AND s.category_id = 1";
    } elseif ($userLocationId) {
        // Department and Health Center users can only see maintenance for their location's assets
        $maintenanceQuery .= " AND fa.current_location_id = $userLocationId";
    }
}

$maintenanceQuery .= " ORDER BY mr.maintenance_date DESC LIMIT 5";

try {
    $maintenanceStmt = mysqli_prepare($conn, $maintenanceQuery);
    if (!$maintenanceStmt) {
        throw new Exception("Prepare failed: " . mysqli_error($conn));
    }
    mysqli_stmt_bind_param($maintenanceStmt, 'ssss', $searchParam, $searchParam, $searchParam, $searchParam);
    if (!mysqli_stmt_execute($maintenanceStmt)) {
        throw new Exception("Execute failed: " . mysqli_stmt_error($maintenanceStmt));
    }
    $maintenanceResult = mysqli_stmt_get_result($maintenanceStmt);

    if ($maintenanceResult) {
        while ($maintenance = mysqli_fetch_assoc($maintenanceResult)) {
            $results['maintenance'][] = [
                'id' => $maintenance['record_id'],
                'asset_id' => $maintenance['asset_id'],
                'asset_name' => $maintenance['asset_name'],
                'sku_code' => $maintenance['sku_code'],
                'type' => $maintenance['maintenance_type'],
                'performed_by' => $maintenance['performed_by'],
                'date' => $maintenance['maintenance_date'],
                'description' => $maintenance['description'],
                'status' => $maintenance['status'],
                'location' => $maintenance['location_name'],
                'url' => "/choims/modules/assets/view.php?id=" . $maintenance['asset_id'] . "#maintenance"
            ];
        }
    }
} catch (Exception $e) {
    // Log the error but continue
    error_log("Maintenance search error: " . $e->getMessage());
}

// Search categories and SKUs
$categoryQuery = "
    SELECT
        c.category_id,
        c.category_name,
        c.category_code,
        c.description,
        COUNT(s.sku_id) as sku_count
    FROM
        categories c
    LEFT JOIN
        sku_master s ON c.category_id = s.category_id AND (s.is_deleted = 0 OR s.is_deleted IS NULL)
    WHERE
        (c.is_deleted = 0 OR c.is_deleted IS NULL)
        AND (
            c.category_name LIKE ?
            OR c.category_code LIKE ?
            OR c.description LIKE ?
        )
    GROUP BY c.category_id
    ORDER BY c.category_name
    LIMIT 5
";

try {
    $categoryStmt = mysqli_prepare($conn, $categoryQuery);
    if (!$categoryStmt) {
        throw new Exception("Prepare failed: " . mysqli_error($conn));
    }
    mysqli_stmt_bind_param($categoryStmt, 'sss', $searchParam, $searchParam, $searchParam);
    if (!mysqli_stmt_execute($categoryStmt)) {
        throw new Exception("Execute failed: " . mysqli_stmt_error($categoryStmt));
    }
    $categoryResult = mysqli_stmt_get_result($categoryStmt);

    if ($categoryResult) {
        while ($category = mysqli_fetch_assoc($categoryResult)) {
            $results['categories'][] = [
                'id' => $category['category_id'],
                'name' => $category['category_name'],
                'code' => $category['category_code'],
                'description' => $category['description'],
                'sku_count' => $category['sku_count'],
                'url' => "/choims/modules/admin/categories.php?id=" . $category['category_id']
            ];
        }
    }
} catch (Exception $e) {
    // Log the error but continue
    error_log("Category search error: " . $e->getMessage());
}

// Search SKUs
$skuQuery = "
    SELECT
        s.sku_id,
        s.sku_code,
        s.sku_name,
        s.description,
        s.specifications,
        c.category_name,
        s.item_type
    FROM
        sku_master s
    JOIN
        categories c ON s.category_id = c.category_id
    WHERE
        (s.is_deleted = 0 OR s.is_deleted IS NULL)
        AND (
            s.sku_name LIKE ?
            OR s.sku_code LIKE ?
            OR s.description LIKE ?
            OR s.specifications LIKE ?
        )
    ORDER BY s.sku_name
    LIMIT 5
";

try {
    $skuStmt = mysqli_prepare($conn, $skuQuery);
    if (!$skuStmt) {
        throw new Exception("Prepare failed: " . mysqli_error($conn));
    }
    mysqli_stmt_bind_param($skuStmt, 'ssss', $searchParam, $searchParam, $searchParam, $searchParam);
    if (!mysqli_stmt_execute($skuStmt)) {
        throw new Exception("Execute failed: " . mysqli_stmt_error($skuStmt));
    }
    $skuResult = mysqli_stmt_get_result($skuStmt);

    if ($skuResult) {
        while ($sku = mysqli_fetch_assoc($skuResult)) {
            $results['skus'][] = [
                'id' => $sku['sku_id'],
                'code' => $sku['sku_code'],
                'name' => $sku['sku_name'],
                'description' => $sku['description'],
                'specifications' => $sku['specifications'],
                'category' => $sku['category_name'],
                'type' => $sku['item_type'],
                'url' => "/choims/modules/admin/sku.php?id=" . $sku['sku_id']
            ];
        }
    }
} catch (Exception $e) {
    // Log the error but continue
    error_log("SKU search error: " . $e->getMessage());
}

// Calculate total results
$results['total'] = count($results['assets']) + count($results['consumables']) + count($results['locations']) + count($results['transactions']) + count($results['batch_transfers']) + count($results['users']) + count($results['suppliers']) + count($results['maintenance']) + count($results['categories']) + count($results['skus']);

// Return results as JSON
header('Content-Type: application/json');
echo json_encode($results);
exit;
