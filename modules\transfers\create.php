<?php
// Start output buffering at the beginning
ob_start();

require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');

// Add the modern transfers create CSS
echo '<link rel="stylesheet" href="/choims/assets/css/transfers-create-modern.css">';

// Ensure user is logged in
requireLogin();

// Prevent superadmin from accessing this page (direct URL protection)
if (strtolower($_SESSION['role']) === 'superadmin') {
    // Redirect to list view instead
    header("Location: /choims/modules/transfers/list.php");
    exit();
}

// Define the ID for Property and Supply/Logistic Management Unit location
$logisticsLocationId = 5; // Based on the database value

$success_message = '';
$error_message = '';

// Get locations for dropdowns
$locationsQuery = "SELECT * FROM locations ORDER BY location_name";
$locationsResult = mysqli_query($conn, $locationsQuery);
$locations = [];
while ($location = mysqli_fetch_assoc($locationsResult)) {
    $locations[$location['location_id']] = $location;
}

// Get user's location for default source
$userLocationId = isset($_SESSION['location_id']) ? $_SESSION['location_id'] : null;

// Check if user is department or health center role
$isRestrictedUser = hasRole('department') || hasRole('healthcenter') || hasRole('himu');

// Check if asset_id is provided in the URL
$preselectedAssetId = isset($_GET['asset_id']) ? sanitizeInput($_GET['asset_id']) : null;

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $asset_id = isset($_POST['asset_id']) && !empty($_POST['asset_id']) ? intval(sanitizeInput($_POST['asset_id'])) : null;
    $quantity = sanitizeInput($_POST['quantity']);
    $source_location_id = sanitizeInput($_POST['source_location_id']);
    $destination_location_id = sanitizeInput($_POST['destination_location_id']);
    $transfer_notes = sanitizeInput($_POST['transfer_notes']);

    // For fixed assets, check required fields before proceeding
    if ($asset_id) {
        // First check if the asset already has an active transfer (individual or batch)
        $activeTransferQuery = "SELECT COUNT(*) as count FROM transfers
                               WHERE asset_id = ?
                               AND status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU')";
        $activeTransferStmt = mysqli_prepare($conn, $activeTransferQuery);
        mysqli_stmt_bind_param($activeTransferStmt, 'i', $asset_id);
        mysqli_stmt_execute($activeTransferStmt);
        $activeTransferResult = mysqli_stmt_get_result($activeTransferStmt);
        $activeTransfer = mysqli_fetch_assoc($activeTransferResult);

        // Also check for active batch transfers
        $activeBatchTransferQuery = "SELECT COUNT(*) as count FROM batch_transfer_assets bta
                                    JOIN batch_transfers bt ON bta.batch_id = bt.batch_id
                                    WHERE bta.asset_id = ?
                                    AND bt.status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU')";
        $activeBatchTransferStmt = mysqli_prepare($conn, $activeBatchTransferQuery);
        mysqli_stmt_bind_param($activeBatchTransferStmt, 'i', $asset_id);
        mysqli_stmt_execute($activeBatchTransferStmt);
        $activeBatchTransferResult = mysqli_stmt_get_result($activeBatchTransferStmt);
        $activeBatchTransfer = mysqli_fetch_assoc($activeBatchTransferResult);

        if ($activeTransfer['count'] > 0) {
            $error_message = "This asset already has an active individual transfer in progress. Please wait for the current transfer to complete before initiating a new one.";
        } elseif ($activeBatchTransfer['count'] > 0) {
            $error_message = "This asset already has an active batch transfer in progress. Please wait for the current transfer to complete before initiating a new one.";
        } else {
            // Check the asset's current status
            $assetStatusQuery = "SELECT status FROM fixed_assets WHERE asset_id = ?";
            $assetStatusStmt = mysqli_prepare($conn, $assetStatusQuery);
            mysqli_stmt_bind_param($assetStatusStmt, 'i', $asset_id);
            mysqli_stmt_execute($assetStatusStmt);
            $assetStatusResult = mysqli_stmt_get_result($assetStatusStmt);
            $assetStatus = mysqli_fetch_assoc($assetStatusResult);

            // If the asset status is "In use", update it to "Available"
            if ($assetStatus && $assetStatus['status'] === 'In use') {
                $updateStatusQuery = "
                    UPDATE fixed_assets
                    SET status = 'Available',
                        updated_at = NOW()
                    WHERE asset_id = ?
                ";
                $updateStatusStmt = mysqli_prepare($conn, $updateStatusQuery);
                mysqli_stmt_bind_param($updateStatusStmt, 'i', $asset_id);
                if (mysqli_stmt_execute($updateStatusStmt)) {
                    // Set a flag to show notification to the user
                    $statusUpdated = true;

                    // Log the status change
                    createAuditLog(
                        $_SESSION['user_id'],
                        'update',
                        'fixed_assets',
                        $asset_id,
                        json_encode(['status' => 'In use']),
                        json_encode(['status' => 'Available'])
                    );
                }
                mysqli_stmt_close($updateStatusStmt);
            }

            // Continue with required fields check
            $checkFieldsQuery = "SELECT local_mr, receipt_type, assigned_to FROM fixed_assets WHERE asset_id = ?";
            $checkFieldsStmt = mysqli_prepare($conn, $checkFieldsQuery);
            mysqli_stmt_bind_param($checkFieldsStmt, 'i', $asset_id);
            mysqli_stmt_execute($checkFieldsStmt);
            $checkResult = mysqli_stmt_get_result($checkFieldsStmt);

            if ($assetData = mysqli_fetch_assoc($checkResult)) {
                $missingFields = [];
                if (empty($assetData['local_mr'])) $missingFields[] = 'local_mr';
                if (empty($assetData['receipt_type'])) $missingFields[] = 'receipt_type';
                if (empty($assetData['assigned_to'])) $missingFields[] = 'assigned_to';

                if (!empty($missingFields) && hasRole('Logistics')) {
                    $redirectUrl = "/choims/modules/assets/edit.php?id={$asset_id}&missing=" . implode(',', $missingFields);
                    header("Location: {$redirectUrl}");
                    exit();
                }
            }
        }
    }

    // Validate inputs
    if (empty($asset_id) || empty($source_location_id) || empty($destination_location_id)) {
        $error_message = "Please select an asset, source location, and destination location.";
    } else if ($source_location_id == $destination_location_id) {
        $error_message = "Source and destination locations cannot be the same.";
    } else if (empty($quantity) || $quantity <= 0) {
        $error_message = "Please enter a valid quantity greater than zero.";
    } else if (empty($error_message)) {
        // Call stored procedure to initiate transfer
        $procedure = "CALL initiate_transfer(?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = mysqli_prepare($conn, $procedure);
        $reference_document = null;
        $inventory_id = null; // Explicitly set inventory_id to null for fixed asset transfers
        mysqli_stmt_bind_param($stmt, 'iiiissss', $asset_id, $inventory_id, $quantity, $source_location_id, $destination_location_id, $_SESSION['user_id'], $transfer_notes, $reference_document);

        if (mysqli_stmt_execute($stmt)) {
            // Get the transfer ID from the procedure result
            $result = mysqli_stmt_get_result($stmt);
            $row = mysqli_fetch_assoc($result);
            $transfer_id = $row['new_transfer_id'];

            // Free the result and close the statement to avoid "Commands out of sync" error
            mysqli_free_result($result);
            mysqli_stmt_close($stmt);

            // Get additional information for detailed logging
            $source_location_name = $locations[$source_location_id]['location_name'] ?? 'Unknown';
            $destination_location_name = $locations[$destination_location_id]['location_name'] ?? 'Unknown';

            // Get item details
            $item_name = '';
            $item_sku = '';
            $sku_id = null;

            if ($asset_id) {
                $asset_query = "SELECT fa.*, sm.sku_name, sm.sku_code, sm.sku_id FROM fixed_assets fa
                                LEFT JOIN sku_master sm ON fa.sku_id = sm.sku_id
                                WHERE fa.asset_id = ?";
                $asset_stmt = mysqli_prepare($conn, $asset_query);
                mysqli_stmt_bind_param($asset_stmt, 'i', $asset_id);
                mysqli_stmt_execute($asset_stmt);
                $asset_result = mysqli_stmt_get_result($asset_stmt);
                if ($asset = mysqli_fetch_assoc($asset_result)) {
                    $item_name = $asset['sku_name'];
                    $item_sku = $asset['sku_code'];
                    $sku_id = $asset['sku_id'];
                }
            }

            // Get the transaction code
            $transaction_code_query = "SELECT transaction_code FROM transfers WHERE transfer_id = ?";
            $transaction_code_stmt = mysqli_prepare($conn, $transaction_code_query);
            mysqli_stmt_bind_param($transaction_code_stmt, 'i', $transfer_id);
            mysqli_stmt_execute($transaction_code_stmt);
            $transaction_code_result = mysqli_stmt_get_result($transaction_code_stmt);
            $transaction_code = '';
            if ($transaction_code_row = mysqli_fetch_assoc($transaction_code_result)) {
                $transaction_code = $transaction_code_row['transaction_code'];
            }
            mysqli_stmt_close($transaction_code_stmt);

            // Create transfer data for logging
            $transfer_data = [
                'asset_id' => $asset_id,
                'sku_id' => $sku_id,
                'item_name' => $item_name,
                'item_sku' => $item_sku,
                'quantity' => $quantity,
                'source_location_id' => $source_location_id,
                'source_location_name' => $source_location_name,
                'destination_location_id' => $destination_location_id,
                'destination_location_name' => $destination_location_name,
                'status' => 'Pending',
                'transfer_notes' => $transfer_notes,
                'transaction_code' => $transaction_code
            ];

            // Log to both systems for backward compatibility
            logActivity(
                $conn,
                'Create',
                'Transfer',
                $transfer_id,
                null,
                json_encode([
                    'asset_id' => $asset_id,
                    'source_location_id' => $source_location_id,
                    'destination_location_id' => $destination_location_id
                ])
            );

            // Log to the detailed audit system
            logTransferAction($conn, $_SESSION['user_id'], 'transfer_initiate', $transfer_id, $transfer_data);

            $success_message = "Transfer initiated successfully! Transfer ID: " . $transfer_id;

            // Redirect to avoid form resubmission
            header("Location: /choims/modules/transfers/view.php?id=" . $transfer_id . "&success=1");
            ob_end_flush();
            exit();
        } else {
            $error_message = "Error initiating transfer: " . mysqli_error($conn);
        }
    }
}
?>

<!-- Enhanced Select2 for searchable dropdowns -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<style>
/* Enhanced Select2 Asset Dropdown Styling */
.select2-result-asset {
    padding: 8px 0;
}

.select2-result-asset .asset-name {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 2px;
}

.select2-result-asset .asset-details {
    font-size: 0.85em;
    color: #6c757d;
    line-height: 1.2;
}

.select2-container--bootstrap-5 .select2-dropdown {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.select2-container--bootstrap-5 .select2-search--dropdown .select2-search__field {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.95rem;
}

.select2-container--bootstrap-5 .select2-search--dropdown .select2-search__field:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.select2-container--bootstrap-5 .select2-results__option--highlighted {
    background-color: #0d6efd;
    color: white;
}

.select2-container--bootstrap-5 .select2-results__option--highlighted .asset-details {
    color: rgba(255, 255, 255, 0.8);
}

/* Invalid state styling for Select2 */
.select2-container.is-invalid .select2-selection {
    border-color: #dc3545;
}

.select2-container.is-invalid .select2-selection:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

/* Auto-focus animation */
.select2-container--open .select2-dropdown {
    animation: fadeInDown 0.3s ease-out;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<!-- Modern dashboard header -->
<header class="dashboard-header animate__animated animate__fadeIn">
    <div class="header-row">
        <div class="title-container">
            <div class="dashboard-title-icon">
                <i class="fas fa-exchange-alt"></i>
            </div>
            <h1 class="dashboard-title">Initiate Transfer</h1>
        </div>
        <div class="dashboard-actions">
            <a href="/choims/modules/transfers/list.php" class="action-btn">
                <i class="fas fa-arrow-left"></i> Back to Transfers
            </a>
        </div>
    </div>
    <p class="dashboard-date"><i class="far fa-calendar-alt"></i> Today is <?php echo date('F d, Y'); ?></p>
</header>

<div class="container-fluid transfer-page">

    <?php if (!empty($success_message)): ?>
    <div class="alert-message alert-success animate__animated animate__fadeInUp" role="alert">
        <div class="alert-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="alert-content">
            <div class="alert-title">Success</div>
            <div class="alert-description"><?php echo $success_message; ?></div>
        </div>
        <button type="button" class="alert-close" aria-label="Close">
            <i class="fas fa-times"></i>
        </button>
    </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
    <div class="alert-message alert-danger animate__animated animate__fadeInUp" role="alert">
        <div class="alert-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="alert-content">
            <div class="alert-title">Error</div>
            <div class="alert-description"><?php echo $error_message; ?></div>
        </div>
        <button type="button" class="alert-close" aria-label="Close">
            <i class="fas fa-times"></i>
        </button>
    </div>
    <?php endif; ?>

    <?php if (isset($statusUpdated) && $statusUpdated): ?>
    <div class="alert-message alert-info animate__animated animate__fadeInUp" role="alert">
        <div class="alert-icon">
            <i class="fas fa-info-circle"></i>
        </div>
        <div class="alert-content">
            <div class="alert-title">Status Changed</div>
            <div class="alert-description">The asset status has been automatically changed from "In use" to "Available" for this transfer.</div>
        </div>
        <button type="button" class="alert-close" aria-label="Close">
            <i class="fas fa-times"></i>
        </button>
    </div>
    <?php endif; ?>

    <div class="filter-card animate__animated animate__fadeInUp animate__faster">
        <div class="filter-card-header">
            <div class="filter-title">
                <i class="fas fa-clipboard-list"></i> Fixed Asset Transfer Details
            </div>
        </div>
        <div class="filter-card-body">
            <form method="post" action="" id="transferForm" class="needs-validation" novalidate onsubmit="return validateForm(this);">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="records-card form-card">
                            <div class="records-card-header">
                                <div class="records-title">
                                    <i class="fas fa-desktop"></i> Select Fixed Asset
                                </div>
                            </div>
                            <div class="records-card-body">

                                <div class="mb-3">
                                    <label for="asset_id" class="form-label">Select Asset <span class="text-danger">*</span></label>
                                    <select class="form-select select2-asset" id="asset_id" name="asset_id" required>
                                        <option value="">-- Search and Select Asset --</option>
                                        <?php
                                        // Get assets for dropdown, filter by user's location if not admin/logistics
                                        $assetsQuery = "SELECT a.asset_id, a.asset_name, a.serial_number, s.sku_name, l.location_name,
                                                        (SELECT COUNT(*) FROM transfers t
                                                         WHERE t.asset_id = a.asset_id
                                                         AND t.status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU')) as has_active_transfer,
                                                        (SELECT t.transfer_id FROM transfers t
                                                         WHERE t.asset_id = a.asset_id
                                                         AND t.status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU') LIMIT 1) as active_transfer_id,
                                                        (SELECT COUNT(*) FROM batch_transfer_assets bta
                                                         JOIN batch_transfers bt ON bta.batch_id = bt.batch_id
                                                         WHERE bta.asset_id = a.asset_id
                                                         AND bt.status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU')) as has_active_batch_transfer,
                                                        (SELECT bt.batch_id FROM batch_transfer_assets bta
                                                         JOIN batch_transfers bt ON bta.batch_id = bt.batch_id
                                                         WHERE bta.asset_id = a.asset_id
                                                         AND bt.status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU') LIMIT 1) as active_batch_id
                                                        FROM fixed_assets a
                                                        JOIN sku_master s ON a.sku_id = s.sku_id
                                                        JOIN locations l ON a.current_location_id = l.location_id
                                                        WHERE a.is_active = 1";

                                        if (!hasRole('GodMode') && !empty($userLocationId)) {
                                            // Filter assets by the user's location for all roles except GodMode
                                            $assetsQuery .= " AND a.current_location_id = " . $userLocationId;
                                        }

                                        $assetsQuery .= " ORDER BY a.asset_name";
                                        $assetsResult = mysqli_query($conn, $assetsQuery);

                                        while ($asset = mysqli_fetch_assoc($assetsResult)) {
                                            $assetInfo = $asset['asset_name'] . ' (' . $asset['sku_name'] . ')';
                                            if (!empty($asset['serial_number'])) {
                                                $assetInfo .= ' - S/N: ' . $asset['serial_number'];
                                            }
                                            $assetInfo .= ' [Location: ' . $asset['location_name'] . ']';

                                            // Check if asset has any active transfer (individual or batch)
                                            $hasAnyActiveTransfer = $asset['has_active_transfer'] > 0 || $asset['has_active_batch_transfer'] > 0;

                                            // Add transfer ongoing badge if asset has active transfer
                                            if ($asset['has_active_transfer'] > 0) {
                                                $assetInfo .= ' <span class="badge bg-info" onclick="window.open(\'/choims/modules/transfers/view.php?id=' . $asset['active_transfer_id'] . '\', \'_blank\')" style="cursor: pointer;" title="Click to view transfer details">Transfer Ongoing</span>';
                                            } elseif ($asset['has_active_batch_transfer'] > 0) {
                                                $assetInfo .= ' <span class="badge bg-info" onclick="window.open(\'/choims/modules/transfers/batch/view.php?id=' . $asset['active_batch_id'] . '\', \'_blank\')" style="cursor: pointer;" title="Click to view batch transfer details">Batch Transfer Ongoing</span>';
                                            }

                                            $selected = ($preselectedAssetId && $preselectedAssetId == $asset['asset_id']) ? 'selected' : '';
                                            $disabled = $hasAnyActiveTransfer ? 'disabled' : '';
                                            echo "<option value='" . $asset['asset_id'] . "' data-location='" . $asset['location_name'] . "' " . $selected . " " . $disabled . ">" . $assetInfo . "</option>";
                                        }
                                        ?>
                                    </select>
                                    <div class="invalid-feedback" id="asset-error-message">Please select an asset to transfer.</div>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle text-primary"></i>
                                        Current location will automatically be selected as source.
                                    </div>
                                </div>
                                <input type="hidden" id="quantity" name="quantity" value="1">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="records-card form-card transfer-location-card">
                            <div class="records-card-header">
                                <div class="records-title">
                                    <i class="fas fa-map-marker-alt"></i> Transfer Locations
                                </div>
                            </div>
                            <div class="records-card-body">

                                <div class="row">
                                    <div class="col-md-5">
                                        <div class="mb-3">
                                            <label for="source_location_id" class="form-label">Source Location <span class="text-danger">*</span></label>
                                            <?php if ($isRestrictedUser && $userLocationId || hasRole('Logistics') && $userLocationId): ?>
                                                <input type="hidden" id="source_location_id" name="source_location_id" value="<?php echo $userLocationId; ?>">
                                                <input type="text" class="form-control bg-light" value="<?php echo isset($locations[$userLocationId]) ? $locations[$userLocationId]['location_name'] : 'Your Location'; ?>" readonly>
                                                <div class="form-text text-muted">
                                                    <i class="fas fa-info-circle"></i> Source location is locked to your assigned location.
                                                </div>
                                            <?php else: ?>
                                                <select class="form-select select2-location" id="source_location_id" name="source_location_id" required>
                                                    <option value="">-- Select Source Location --</option>
                                                    <?php
                                                    foreach ($locations as $location) {
                                                        $selected = ($location['location_id'] == $userLocationId) ? 'selected' : '';
                                                        echo "<option value='" . $location['location_id'] . "' " . $selected . ">" . $location['location_name'] . "</option>";
                                                    }
                                                    ?>
                                                </select>
                                                <div class="invalid-feedback">Please select a source location.</div>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="col-md-2">
                                        <div class="text-center my-2">
                                            <div class="transfer-arrow-container">
                                                <div class="transfer-arrow">
                                                    <i class="fas fa-arrow-right fa-2x text-primary"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-5">
                                        <div class="mb-3">
                                            <label for="destination_location_id" class="form-label">Destination Location <span class="text-danger">*</span></label>
                                            <?php if ($isRestrictedUser): ?>
                                                <input type="hidden" id="destination_location_id" name="destination_location_id" value="<?php echo $logisticsLocationId; ?>">
                                                <input type="text" class="form-control bg-light" value="PROPERTY AND SUPPLY/LOGISTIC MANAGEMENT UNIT" readonly>
                                                <div class="form-text text-muted">
                                                    <i class="fas fa-info-circle"></i> Destination is locked to Property and Supply/Logistic Management Unit.
                                                </div>
                                            <?php else: ?>
                                                <select class="form-select" id="destination_location_id" name="destination_location_id" required>
                                                    <option value="">-- Select Destination Location --</option>
                                                    <?php
                                                    foreach ($locations as $location) {
                                                        echo "<option value='" . $location['location_id'] . "'>" . $location['location_name'] . "</option>";
                                                    }
                                                    ?>
                                                </select>
                                                <div class="invalid-feedback">Please select a destination location.</div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="records-card form-card">
                        <div class="records-card-header">
                            <div class="records-title">
                                <i class="fas fa-clipboard"></i> Additional Information
                            </div>
                        </div>
                        <div class="records-card-body">

                            <div class="mb-3">
                                <label for="transfer_notes" class="form-label">Transfer Notes</label>
                                <textarea class="form-control" id="transfer_notes" name="transfer_notes" rows="2" placeholder="Enter any additional notes or reasons for this transfer..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="filter-actions mt-4">
                    <div class="filter-buttons-container">
                        <button type="button" class="filter-btn filter-btn-secondary" onclick="window.history.back();">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                        <button type="reset" class="filter-btn filter-btn-secondary">
                            <i class="fas fa-redo"></i> Reset
                        </button>
                        <button type="submit" class="filter-btn filter-btn-primary">
                            <i class="fas fa-paper-plane"></i> Submit Transfer
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Global form validation function
function validateForm(form) {
    // Check if asset is selected
    const assetSelect = document.getElementById('asset_id');
    if (!assetSelect.value) {
        // Show error message
        const errorMessage = document.getElementById('asset-error-message');
        if (errorMessage) {
            errorMessage.style.display = 'block';
            errorMessage.style.color = '#dc3545';
            errorMessage.style.fontWeight = 'bold';
            errorMessage.classList.add('animate__animated', 'animate__headShake');
            setTimeout(() => {
                errorMessage.classList.remove('animate__headShake');
            }, 1000);
        }

        // Add invalid class to the dropdown
        assetSelect.classList.add('is-invalid');

        // Scroll to the asset select field
        assetSelect.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // Focus on the dropdown
        setTimeout(() => {
            assetSelect.focus();
        }, 500);

        return false; // Prevent form submission
    }

    // Check source and destination locations
    const sourceLocation = document.getElementById('source_location_id').value;
    const destinationLocation = document.getElementById('destination_location_id').value;

    if (sourceLocation && destinationLocation && sourceLocation === destinationLocation) {
        alert('Source and destination locations cannot be the same.');
        return false;
    }

    return true; // Allow form submission
}

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Select2 for asset dropdown with enhanced search
    $('.select2-asset').select2({
        theme: 'bootstrap-5',
        width: '100%',
        placeholder: 'Search for an asset...',
        allowClear: true,
        matcher: function(params, data) {
            // If there are no search terms, return all data
            if ($.trim(params.term) === '') {
                return data;
            }

            // Skip if there is no 'text' property
            if (typeof data.text === 'undefined') {
                return null;
            }

            // Search in multiple fields: asset name, SKU, serial number, location
            const searchTerm = params.term.toLowerCase();
            const text = data.text.toLowerCase();

            // Check if search term matches any part of the option text
            if (text.indexOf(searchTerm) > -1) {
                return data;
            }

            // Return null if the term should not be displayed
            return null;
        },
        templateResult: function(data) {
            if (data.loading) {
                return data.text;
            }

            // Custom formatting for search results
            if (!data.id) {
                return data.text;
            }

            // Parse the option text to highlight different parts
            const text = data.text;
            const parts = text.split(' - ');

            if (parts.length >= 2) {
                const $result = $(
                    '<div class="select2-result-asset">' +
                        '<div class="asset-name">' + parts[0] + '</div>' +
                        '<div class="asset-details text-muted small">' + parts.slice(1).join(' - ') + '</div>' +
                    '</div>'
                );
                return $result;
            }

            return data.text;
        },
        templateSelection: function(data) {
            if (!data.id) {
                return data.text;
            }

            // Show just the asset name in the selection
            const text = data.text;
            const parts = text.split(' (');
            return parts[0]; // Just the asset name part
        }
    });

    // Auto-focus on the asset search when dropdown is opened
    $('#asset_id').on('select2:open', function() {
        // Multiple attempts to ensure focus works
        setTimeout(function() {
            const searchField = $('.select2-search__field');
            searchField.focus();
            searchField.click();
            searchField[0].focus(); // Direct DOM focus
        }, 50);

        setTimeout(function() {
            const searchField = $('.select2-search__field');
            searchField.focus();
            searchField[0].focus(); // Direct DOM focus
        }, 150);
    });

    // Auto-focus on page load - open dropdown and focus search
    setTimeout(function() {
        $('.select2-asset').select2('open');

        // Multiple attempts to ensure focus on page load
        setTimeout(function() {
            const searchField = $('.select2-search__field');
            searchField.focus();
            searchField.click();
            searchField[0].focus(); // Direct DOM focus
        }, 100);

        setTimeout(function() {
            const searchField = $('.select2-search__field');
            searchField.focus();
            searchField[0].focus(); // Direct DOM focus
        }, 300);
    }, 800);

    // Add validation handling for Select2 asset dropdown
    $('#asset_id').on('change', function() {
        if (!this.value) {
            $(this).addClass('is-invalid');
            $(this).next('.select2-container').addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.select2-container').removeClass('is-invalid');
        }
    });

    // Initialize Select2 for location dropdowns
    $('.select2-location').select2({
        theme: 'bootstrap-5',
        width: '100%'
    });

    // Check if we're in restricted mode (department or health center)
    const isRestrictedMode = <?php echo $isRestrictedUser ? 'true' : 'false'; ?>;

    const sourceLocationSelect = document.getElementById('source_location_id');
    const assetSelect = document.getElementById('asset_id');

    // Trigger change event for preselected asset to update location
    if (assetSelect && assetSelect.value) {
        $(assetSelect).trigger('change');
    }

    // Check for required fields in fixed assets
    function checkAssetRequiredFields(assetId) {
        if (!assetId || !assetId.trim()) return;

        // Only perform check for logistics users
        <?php if (hasRole('Logistics')): ?>
        // Create form data
        const formData = new FormData();
        formData.append('asset_id', assetId);

        // Check for missing fields via AJAX
        fetch('/choims/modules/assets/check_required_fields.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.missingFields && data.missingFields.length > 0) {
                // Redirect to edit page with missing fields
                const redirectUrl = `/choims/modules/assets/edit.php?id=${assetId}&missing=${data.missingFields.join(',')}`;
                window.location.href = redirectUrl;
            }
        })
        .catch(error => console.error('Error checking required fields:', error));
        <?php endif; ?>
    }

    // When asset is selected, check required fields
    if (assetSelect) {
        assetSelect.addEventListener('change', function() {
            const assetId = this.value;
            if (assetId) {
                checkAssetRequiredFields(assetId);

                // Original asset selection behavior...
                const selectedOption = this.options[this.selectedIndex];
                if (selectedOption.value) {
                    const locationName = selectedOption.dataset.location;

                    // Find the location ID by name and set source location
                    for (let i = 0; i < sourceLocationSelect.options.length; i++) {
                        if (sourceLocationSelect.options[i].text.includes(locationName)) {
                            sourceLocationSelect.value = sourceLocationSelect.options[i].value;
                            $(sourceLocationSelect).trigger('change'); // Trigger change for Select2
                            break;
                        }
                    }
                }
            }
        });
    }

    // Visual feedback on location selection
    const destinationLocationSelect = document.getElementById('destination_location_id');
    if (sourceLocationSelect && destinationLocationSelect) {
        const updateTransferArrow = function() {
            const sourceValue = sourceLocationSelect.value;
            const destValue = destinationLocationSelect.value;

            const arrowElement = document.querySelector('.transfer-arrow i');
            const arrowContainer = document.querySelector('.transfer-arrow');
            const arrowWrapper = document.querySelector('.transfer-arrow-container');

            if (sourceValue && destValue) {
                if (sourceValue === destValue) {
                    arrowElement.className = 'fas fa-ban fa-2x text-danger';
                    arrowContainer.classList.remove('transfer-active');
                    arrowWrapper.classList.add('error');
                } else {
                    arrowElement.className = 'fas fa-arrow-right fa-2x text-success';
                    arrowContainer.classList.add('transfer-active');
                    arrowWrapper.classList.add('active');
                    arrowWrapper.classList.remove('error');
                }
            } else {
                arrowElement.className = 'fas fa-arrow-right fa-2x text-primary';
                arrowContainer.classList.remove('transfer-active');
                arrowWrapper.classList.remove('active');
                arrowWrapper.classList.remove('error');
            }
        };

        sourceLocationSelect.addEventListener('change', updateTransferArrow);
        destinationLocationSelect.addEventListener('change', updateTransferArrow);
    }

    // Form validation - add class handling
    const form = document.getElementById('transferForm');
    if (form) {
        form.addEventListener('submit', function(event) {
            // Add was-validated class for Bootstrap validation styles
            form.classList.add('was-validated');

            // The actual validation is handled by the validateForm function
            // which is called via the onsubmit attribute
        }, false);
    }

    // Add animations when form fields change
    const formInputs = document.querySelectorAll('input, select, textarea');
    formInputs.forEach(input => {
        input.addEventListener('change', function() {
            const card = this.closest('.form-card');
            if (card) {
                card.classList.add('pulse-animation');
                setTimeout(() => {
                    card.classList.remove('pulse-animation');
                }, 500);
            }
        });
    });

    // Add dismiss functionality to alerts
    const alerts = document.querySelectorAll('.alert-message');
    alerts.forEach(function(alert) {
        const closeButton = alert.querySelector('.alert-close');
        if (closeButton) {
            closeButton.addEventListener('click', function() {
                alert.classList.add('animate__fadeOutUp');
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }
    });
});
</script>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>